import React from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Avatar,
  Typography,
  Box,
  Button
} from '@mui/material'
import {
  Visibility as ViewIcon,
  EventNote as BookingIcon
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'

const RecentBookings = ({ bookings = [] }) => {
  const navigate = useNavigate()

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed':
        return 'success'
      case 'pending':
        return 'warning'
      case 'cancelled':
        return 'error'
      case 'completed':
        return 'info'
      default:
        return 'default'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'confirmed':
        return 'مؤكد'
      case 'pending':
        return 'في الانتظار'
      case 'cancelled':
        return 'ملغي'
      case 'completed':
        return 'مكتمل'
      default:
        return status
    }
  }

  const formatDate = (dateString) => {
    try {
      return new Date(dateString).toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    } catch (error) {
      return dateString
    }
  }

  const formatTime = (timeString) => {
    try {
      return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      })
    } catch (error) {
      return timeString
    }
  }

  if (!bookings || bookings.length === 0) {
    return (
      <Box className="text-center py-8">
        <Avatar
          sx={{
            bgcolor: '#f3f4f6',
            width: 64,
            height: 64,
            margin: '0 auto 16px'
          }}
        >
          <BookingIcon sx={{ color: '#9ca3af' }} />
        </Avatar>
        <Typography
          variant="body1"
          className="text-gray-500"
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          لا توجد حجوزات حديثة
        </Typography>
        <Button
          variant="outlined"
          onClick={() => navigate('/bookings')}
          className="mt-4"
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          عرض جميع الحجوزات
        </Button>
      </Box>
    )
  }

  return (
    <TableContainer>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
              العميل
            </TableCell>
            <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
              القاعة
            </TableCell>
            <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
              التاريخ
            </TableCell>
            <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
              الوقت
            </TableCell>
            <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
              الحالة
            </TableCell>
            <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
              المبلغ
            </TableCell>
            <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
              الإجراءات
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {bookings.map((booking) => (
            <TableRow 
              key={booking.id}
              hover
              className="cursor-pointer"
              onClick={() => navigate(`/bookings/${booking.id}`)}
            >
              <TableCell>
                <div className="flex items-center space-x-3 space-x-reverse">
                  <Avatar
                    sx={{
                      bgcolor: '#0ea5e9',
                      width: 32,
                      height: 32,
                      fontSize: '14px'
                    }}
                  >
                    {booking.customerName?.charAt(0) || 'ع'}
                  </Avatar>
                  <div>
                    <Typography
                      variant="body2"
                      className="font-medium"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {booking.customerName || 'غير محدد'}
                    </Typography>
                    <Typography
                      variant="caption"
                      className="text-gray-500"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {booking.customerPhone || ''}
                    </Typography>
                  </div>
                </div>
              </TableCell>
              
              <TableCell>
                <Typography
                  variant="body2"
                  className="font-medium"
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  {booking.hallName || 'غير محدد'}
                </Typography>
                <Typography
                  variant="caption"
                  className="text-gray-500"
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  {booking.hallType || ''}
                </Typography>
              </TableCell>
              
              <TableCell>
                <Typography
                  variant="body2"
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  {formatDate(booking.date)}
                </Typography>
              </TableCell>
              
              <TableCell>
                <Typography
                  variant="body2"
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  {formatTime(booking.startTime)} - {formatTime(booking.endTime)}
                </Typography>
              </TableCell>
              
              <TableCell>
                <Chip
                  label={getStatusText(booking.status)}
                  color={getStatusColor(booking.status)}
                  size="small"
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                />
              </TableCell>
              
              <TableCell>
                <Typography
                  variant="body2"
                  className="font-medium"
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  {booking.totalAmount || 0} ر.س
                </Typography>
              </TableCell>
              
              <TableCell>
                <Button
                  size="small"
                  startIcon={<ViewIcon />}
                  onClick={(e) => {
                    e.stopPropagation()
                    navigate(`/bookings/${booking.id}`)
                  }}
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  عرض
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      
      {bookings.length > 0 && (
        <Box className="p-4 text-center border-t">
          <Button
            variant="outlined"
            onClick={() => navigate('/bookings')}
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            عرض جميع الحجوزات
          </Button>
        </Box>
      )}
    </TableContainer>
  )
}

export default RecentBookings
