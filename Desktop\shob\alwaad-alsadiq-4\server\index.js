const express = require('express')
const cors = require('cors')
const bcrypt = require('bcryptjs')
const jwt = require('jsonwebtoken')
const { Pool } = require('pg')

const app = express()
const PORT = process.env.PORT || 5000

// Middleware
app.use(cors())
app.use(express.json())

// Database connection
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'alwaad_alsadiq_4',
  password: process.env.DB_PASSWORD || 'password',
  port: process.env.DB_PORT || 5432,
})

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here'

// Auth middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]

  if (!token) {
    return res.status(401).json({ message: 'رمز المصادقة مطلوب' })
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'رمز المصادقة غير صحيح' })
    }
    req.user = user
    next()
  })
}

// Role-based access control
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ message: 'ليس لديك الصلاحيات اللازمة' })
    }
    next()
  }
}

// Initialize database tables
const initDatabase = async () => {
  try {
    // Users table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        phone VARCHAR(20),
        password VARCHAR(255) NOT NULL,
        role VARCHAR(50) DEFAULT 'customer',
        status VARCHAR(20) DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Halls table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS halls (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        type VARCHAR(50) DEFAULT 'general',
        capacity INTEGER NOT NULL,
        price_per_hour DECIMAL(10,2) NOT NULL,
        location VARCHAR(255),
        amenities TEXT[],
        rules TEXT[],
        working_hours JSONB DEFAULT '{"start": "08:00", "end": "23:00"}',
        status VARCHAR(20) DEFAULT 'active',
        images TEXT[],
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Bookings table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS bookings (
        id SERIAL PRIMARY KEY,
        hall_id INTEGER REFERENCES halls(id),
        customer_id INTEGER REFERENCES users(id),
        customer_name VARCHAR(255) NOT NULL,
        customer_phone VARCHAR(20),
        customer_email VARCHAR(255),
        date DATE NOT NULL,
        start_time TIME NOT NULL,
        end_time TIME NOT NULL,
        duration_hours DECIMAL(4,2) NOT NULL,
        price_per_hour DECIMAL(10,2) NOT NULL,
        total_amount DECIMAL(10,2) NOT NULL,
        status VARCHAR(20) DEFAULT 'pending',
        notes TEXT,
        payment_status VARCHAR(20) DEFAULT 'pending',
        payment_method VARCHAR(50),
        created_by INTEGER REFERENCES users(id),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Notifications table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS notifications (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id),
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type VARCHAR(50) DEFAULT 'info',
        read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create default admin user
    const adminExists = await pool.query('SELECT id FROM users WHERE email = $1', ['<EMAIL>'])
    if (adminExists.rows.length === 0) {
      const hashedPassword = await bcrypt.hash('admin123', 10)
      await pool.query(`
        INSERT INTO users (name, email, password, role)
        VALUES ($1, $2, $3, $4)
      `, ['مدير النظام', '<EMAIL>', hashedPassword, 'admin'])
    }

    // Create sample halls
    const hallsCount = await pool.query('SELECT COUNT(*) FROM halls')
    if (parseInt(hallsCount.rows[0].count) === 0) {
      const sampleHalls = [
        {
          name: 'قاعة VIP الذهبية',
          description: 'قاعة فاخرة مجهزة بأحدث التقنيات',
          type: 'vip',
          capacity: 100,
          price_per_hour: 500,
          location: 'الطابق الثاني',
          amenities: ['تكييف مركزي', 'نظام صوتي متطور', 'إضاءة LED', 'واي فاي مجاني'],
          rules: ['ممنوع التدخين', 'الالتزام بالمواعيد', 'عدم إحضار المأكولات من الخارج']
        },
        {
          name: 'قاعة الاجتماعات الرئيسية',
          description: 'قاعة مثالية للاجتماعات والمؤتمرات',
          type: 'meeting',
          capacity: 50,
          price_per_hour: 200,
          location: 'الطابق الأول',
          amenities: ['بروجكتر', 'شاشة عرض كبيرة', 'نظام صوتي', 'طاولة اجتماعات'],
          rules: ['الحضور قبل 15 دقيقة', 'إغلاق الهواتف المحمولة']
        },
        {
          name: 'قاعة الألعاب الترفيهية',
          description: 'قاعة مخصصة للألعاب والأنشطة الترفيهية',
          type: 'gaming',
          capacity: 30,
          price_per_hour: 150,
          location: 'الطابق الأرضي',
          amenities: ['ألعاب إلكترونية', 'طاولات بلياردو', 'نظام ترفيهي'],
          rules: ['للأعمار فوق 12 سنة', 'المحافظة على الأجهزة']
        }
      ]

      for (const hall of sampleHalls) {
        await pool.query(`
          INSERT INTO halls (name, description, type, capacity, price_per_hour, location, amenities, rules)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        `, [
          hall.name,
          hall.description,
          hall.type,
          hall.capacity,
          hall.price_per_hour,
          hall.location,
          hall.amenities,
          hall.rules
        ])
      }
    }

    console.log('Database initialized successfully')
  } catch (error) {
    console.error('Database initialization error:', error)
  }
}

// Auth Routes
app.post('/api/auth/register', async (req, res) => {
  try {
    const { name, email, phone, password, role = 'customer' } = req.body

    // Check if user exists
    const userExists = await pool.query('SELECT id FROM users WHERE email = $1', [email])
    if (userExists.rows.length > 0) {
      return res.status(400).json({ message: 'البريد الإلكتروني مستخدم بالفعل' })
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10)

    // Create user
    const result = await pool.query(`
      INSERT INTO users (name, email, phone, password, role)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING id, name, email, phone, role, status, created_at
    `, [name, email, phone, hashedPassword, role])

    res.status(201).json({
      message: 'تم إنشاء الحساب بنجاح',
      user: result.rows[0]
    })
  } catch (error) {
    console.error('Registration error:', error)
    res.status(500).json({ message: 'خطأ في الخادم' })
  }
})

app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body

    // Find user
    const result = await pool.query('SELECT * FROM users WHERE email = $1', [email])
    if (result.rows.length === 0) {
      return res.status(401).json({ message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة' })
    }

    const user = result.rows[0]

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password)
    if (!isValidPassword) {
      return res.status(401).json({ message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة' })
    }

    // Check if user is active
    if (user.status !== 'active') {
      return res.status(401).json({ message: 'الحساب غير مفعل' })
    }

    // Generate JWT
    const token = jwt.sign(
      { id: user.id, email: user.email, role: user.role },
      JWT_SECRET,
      { expiresIn: '24h' }
    )

    // Remove password from response
    delete user.password

    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user
    })
  } catch (error) {
    console.error('Login error:', error)
    res.status(500).json({ message: 'خطأ في الخادم' })
  }
})

app.get('/api/auth/me', authenticateToken, async (req, res) => {
  try {
    const result = await pool.query(
      'SELECT id, name, email, phone, role, status, created_at FROM users WHERE id = $1',
      [req.user.id]
    )

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'المستخدم غير موجود' })
    }

    res.json(result.rows[0])
  } catch (error) {
    console.error('Get user error:', error)
    res.status(500).json({ message: 'خطأ في الخادم' })
  }
})

// Halls Routes
app.get('/api/halls', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT * FROM halls 
      ORDER BY created_at DESC
    `)

    res.json(result.rows)
  } catch (error) {
    console.error('Get halls error:', error)
    res.status(500).json({ message: 'خطأ في الخادم' })
  }
})

app.post('/api/halls', authenticateToken, requireRole(['admin', 'manager']), async (req, res) => {
  try {
    const {
      name,
      description,
      type,
      capacity,
      pricePerHour,
      location,
      amenities,
      rules,
      workingHours,
      status
    } = req.body

    const result = await pool.query(`
      INSERT INTO halls (
        name, description, type, capacity, price_per_hour, 
        location, amenities, rules, working_hours, status
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `, [
      name,
      description,
      type,
      capacity,
      pricePerHour,
      location,
      amenities || [],
      rules || [],
      workingHours || { start: '08:00', end: '23:00' },
      status || 'active'
    ])

    res.status(201).json(result.rows[0])
  } catch (error) {
    console.error('Create hall error:', error)
    res.status(500).json({ message: 'خطأ في الخادم' })
  }
})

// Start server
const startServer = async () => {
  await initDatabase()
  
  app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`)
    console.log(`Database: ${process.env.DB_NAME || 'alwaad_alsadiq_4'}`)
    console.log('Default admin credentials:')
    console.log('Email: <EMAIL>')
    console.log('Password: admin123')
  })
}

startServer().catch(console.error)
