import React from 'react'
import { Box, CircularProgress, Typography } from '@mui/material'

const LoadingSpinner = ({ message = 'جاري التحميل...', size = 40 }) => {
  return (
    <Box 
      className="flex flex-col items-center justify-center min-h-screen bg-gray-50"
      sx={{ 
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 9999,
        backgroundColor: 'rgba(248, 250, 252, 0.9)',
        backdropFilter: 'blur(4px)'
      }}
    >
      <div className="text-center">
        <CircularProgress 
          size={size} 
          thickness={4}
          sx={{ 
            color: '#0ea5e9',
            mb: 2
          }}
        />
        <Typography 
          variant="body1" 
          className="text-gray-600 font-medium"
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          {message}
        </Typography>
      </div>
    </Box>
  )
}

export default LoadingSpinner
