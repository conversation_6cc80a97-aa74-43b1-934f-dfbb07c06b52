import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Button,
  Chip,
  IconButton,
  Dialog,
  Fab,
  TextField,
  MenuItem,
  InputAdornment
} from '@mui/material'
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  MeetingRoom as HallIcon
} from '@mui/icons-material'

import { fetchHalls, deleteHall } from '../../store/slices/hallsSlice'
import HallForm from '../../components/Halls/HallForm'
import HallDetails from '../../components/Halls/HallDetails'
import ConfirmDialog from '../../components/Common/ConfirmDialog'
import LoadingSpinner from '../../components/LoadingSpinner/LoadingSpinner'

const Halls = () => {
  const dispatch = useDispatch()
  const { halls, isLoading, error } = useSelector((state) => state.halls)
  const { user } = useSelector((state) => state.auth)

  const [selectedHall, setSelectedHall] = useState(null)
  const [showForm, setShowForm] = useState(false)
  const [showDetails, setShowDetails] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [hallToDelete, setHallToDelete] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')
  const [filterCapacity, setFilterCapacity] = useState('all')

  useEffect(() => {
    dispatch(fetchHalls())
  }, [dispatch])

  const canManageHalls = ['admin', 'manager'].includes(user?.role)

  const filteredHalls = halls.filter(hall => {
    const matchesSearch = hall.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         hall.description?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesType = filterType === 'all' || hall.type === filterType
    
    const matchesCapacity = filterCapacity === 'all' || 
                           (filterCapacity === 'small' && hall.capacity <= 50) ||
                           (filterCapacity === 'medium' && hall.capacity > 50 && hall.capacity <= 100) ||
                           (filterCapacity === 'large' && hall.capacity > 100)

    return matchesSearch && matchesType && matchesCapacity
  })

  const handleAddHall = () => {
    setSelectedHall(null)
    setShowForm(true)
  }

  const handleEditHall = (hall) => {
    setSelectedHall(hall)
    setShowForm(true)
  }

  const handleViewHall = (hall) => {
    setSelectedHall(hall)
    setShowDetails(true)
  }

  const handleDeleteHall = (hall) => {
    setHallToDelete(hall)
    setShowDeleteDialog(true)
  }

  const confirmDelete = async () => {
    if (hallToDelete) {
      await dispatch(deleteHall(hallToDelete.id))
      setShowDeleteDialog(false)
      setHallToDelete(null)
    }
  }

  const getHallTypeText = (type) => {
    const types = {
      'vip': 'VIP',
      'meeting': 'اجتماعات',
      'conference': 'مؤتمرات',
      'wedding': 'أفراح',
      'gaming': 'ألعاب',
      'general': 'عامة'
    }
    return types[type] || type
  }

  const getHallTypeColor = (type) => {
    const colors = {
      'vip': 'secondary',
      'meeting': 'primary',
      'conference': 'info',
      'wedding': 'error',
      'gaming': 'warning',
      'general': 'default'
    }
    return colors[type] || 'default'
  }

  const getCapacityText = (capacity) => {
    if (capacity <= 50) return 'صغيرة'
    if (capacity <= 100) return 'متوسطة'
    return 'كبيرة'
  }

  if (isLoading) {
    return <LoadingSpinner message="جاري تحميل القاعات..." />
  }

  return (
    <Box className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <Typography 
            variant="h4" 
            className="font-bold text-gray-800"
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            إدارة القاعات
          </Typography>
          <Typography 
            variant="body2" 
            className="text-gray-600 mt-1"
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            عرض وإدارة جميع القاعات المتاحة
          </Typography>
        </div>
        
        {canManageHalls && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddHall}
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            إضافة قاعة جديدة
          </Button>
        )}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="البحث في القاعات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon className="text-gray-400" />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiInputBase-input': {
                    fontFamily: 'Cairo, sans-serif'
                  }
                }}
              />
            </Grid>
            
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                select
                label="نوع القاعة"
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                sx={{
                  '& .MuiInputLabel-root': {
                    fontFamily: 'Cairo, sans-serif'
                  }
                }}
              >
                <MenuItem value="all">جميع الأنواع</MenuItem>
                <MenuItem value="vip">VIP</MenuItem>
                <MenuItem value="meeting">اجتماعات</MenuItem>
                <MenuItem value="conference">مؤتمرات</MenuItem>
                <MenuItem value="wedding">أفراح</MenuItem>
                <MenuItem value="gaming">ألعاب</MenuItem>
                <MenuItem value="general">عامة</MenuItem>
              </TextField>
            </Grid>
            
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                select
                label="السعة"
                value={filterCapacity}
                onChange={(e) => setFilterCapacity(e.target.value)}
                sx={{
                  '& .MuiInputLabel-root': {
                    fontFamily: 'Cairo, sans-serif'
                  }
                }}
              >
                <MenuItem value="all">جميع الأحجام</MenuItem>
                <MenuItem value="small">صغيرة (حتى 50)</MenuItem>
                <MenuItem value="medium">متوسطة (51-100)</MenuItem>
                <MenuItem value="large">كبيرة (أكثر من 100)</MenuItem>
              </TextField>
            </Grid>
            
            <Grid item xs={12} md={2}>
              <Typography 
                variant="body2" 
                className="text-gray-600"
                sx={{ fontFamily: 'Cairo, sans-serif' }}
              >
                {filteredHalls.length} قاعة
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Halls Grid */}
      {filteredHalls.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <HallIcon sx={{ fontSize: 64, color: '#9ca3af', mb: 2 }} />
            <Typography 
              variant="h6" 
              className="text-gray-500 mb-2"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              لا توجد قاعات
            </Typography>
            <Typography 
              variant="body2" 
              className="text-gray-400"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              {searchTerm || filterType !== 'all' || filterCapacity !== 'all' 
                ? 'لا توجد قاعات تطابق معايير البحث'
                : 'لم يتم إضافة أي قاعات بعد'
              }
            </Typography>
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={3}>
          {filteredHalls.map((hall) => (
            <Grid item xs={12} sm={6} lg={4} key={hall.id}>
              <Card 
                className="h-full hover:shadow-lg transition-shadow duration-300"
                sx={{ borderRadius: '12px' }}
              >
                <CardMedia
                  component="div"
                  className="h-48 bg-gradient-to-br from-blue-400 to-purple-500 relative"
                >
                  <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                    <HallIcon sx={{ fontSize: 48, color: 'white' }} />
                  </div>
                  <div className="absolute top-3 right-3">
                    <Chip
                      label={getHallTypeText(hall.type)}
                      color={getHallTypeColor(hall.type)}
                      size="small"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    />
                  </div>
                </CardMedia>
                
                <CardContent className="p-4">
                  <Typography 
                    variant="h6" 
                    className="font-semibold mb-2"
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    {hall.name}
                  </Typography>
                  
                  <Typography 
                    variant="body2" 
                    className="text-gray-600 mb-3 line-clamp-2"
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    {hall.description || 'لا يوجد وصف متاح'}
                  </Typography>
                  
                  <div className="flex justify-between items-center mb-3">
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <div className="text-center">
                        <Typography 
                          variant="caption" 
                          className="text-gray-500 block"
                          sx={{ fontFamily: 'Cairo, sans-serif' }}
                        >
                          السعة
                        </Typography>
                        <Typography 
                          variant="body2" 
                          className="font-medium"
                          sx={{ fontFamily: 'Cairo, sans-serif' }}
                        >
                          {hall.capacity}
                        </Typography>
                      </div>
                      
                      <div className="text-center">
                        <Typography 
                          variant="caption" 
                          className="text-gray-500 block"
                          sx={{ fontFamily: 'Cairo, sans-serif' }}
                        >
                          السعر/ساعة
                        </Typography>
                        <Typography 
                          variant="body2" 
                          className="font-medium"
                          sx={{ fontFamily: 'Cairo, sans-serif' }}
                        >
                          {hall.pricePerHour} ر.س
                        </Typography>
                      </div>
                    </div>
                    
                    <Chip
                      label={hall.status === 'active' ? 'متاحة' : 'غير متاحة'}
                      color={hall.status === 'active' ? 'success' : 'error'}
                      size="small"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    />
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <Button
                      size="small"
                      startIcon={<ViewIcon />}
                      onClick={() => handleViewHall(hall)}
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      عرض
                    </Button>
                    
                    {canManageHalls && (
                      <div className="space-x-1 space-x-reverse">
                        <IconButton
                          size="small"
                          onClick={() => handleEditHall(hall)}
                          color="primary"
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteHall(hall)}
                          color="error"
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Floating Action Button for Mobile */}
      {canManageHalls && (
        <Fab
          color="primary"
          aria-label="add hall"
          onClick={handleAddHall}
          sx={{
            position: 'fixed',
            bottom: 16,
            left: 16,
            display: { xs: 'flex', md: 'none' }
          }}
        >
          <AddIcon />
        </Fab>
      )}

      {/* Hall Form Dialog */}
      <Dialog
        open={showForm}
        onClose={() => setShowForm(false)}
        maxWidth="md"
        fullWidth
      >
        <HallForm
          hall={selectedHall}
          onClose={() => setShowForm(false)}
          onSuccess={() => {
            setShowForm(false)
            dispatch(fetchHalls())
          }}
        />
      </Dialog>

      {/* Hall Details Dialog */}
      <Dialog
        open={showDetails}
        onClose={() => setShowDetails(false)}
        maxWidth="md"
        fullWidth
      >
        <HallDetails
          hall={selectedHall}
          onClose={() => setShowDetails(false)}
          onEdit={() => {
            setShowDetails(false)
            setShowForm(true)
          }}
        />
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={showDeleteDialog}
        title="حذف القاعة"
        message={`هل أنت متأكد من حذف قاعة "${hallToDelete?.name}"؟ هذا الإجراء لا يمكن التراجع عنه.`}
        onConfirm={confirmDelete}
        onCancel={() => {
          setShowDeleteDialog(false)
          setHallToDelete(null)
        }}
        confirmText="حذف"
        cancelText="إلغاء"
        severity="error"
      />
    </Box>
  )
}

export default Halls
