import React, { useState } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Avatar,
  Grid,
  Divider,
  Alert,
  Tab,
  Tabs,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip
} from '@mui/material'
import {
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Lock as LockIcon,
  Save as SaveIcon,
  History as HistoryIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon
} from '@mui/icons-material'

const Profile = () => {
  const dispatch = useDispatch()
  const { user } = useSelector((state) => state.auth)
  
  const [activeTab, setActiveTab] = useState(0)
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [messageType, setMessageType] = useState('success')

  // Profile form state
  const [profileData, setProfileData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    phone: user?.phone || ''
  })

  // Password form state
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue)
    setMessage('')
  }

  const handleProfileChange = (e) => {
    const { name, value } = e.target
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handlePasswordChange = (e) => {
    const { name, value } = e.target
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleProfileSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setMessage('')

    try {
      // API call to update profile
      // await dispatch(updateProfile(profileData)).unwrap()
      setMessage('تم تحديث الملف الشخصي بنجاح')
      setMessageType('success')
    } catch (error) {
      setMessage('حدث خطأ أثناء تحديث الملف الشخصي')
      setMessageType('error')
    } finally {
      setLoading(false)
    }
  }

  const handlePasswordSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setMessage('')

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setMessage('كلمة المرور الجديدة غير متطابقة')
      setMessageType('error')
      setLoading(false)
      return
    }

    if (passwordData.newPassword.length < 6) {
      setMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
      setMessageType('error')
      setLoading(false)
      return
    }

    try {
      // API call to change password
      // await dispatch(changePassword(passwordData)).unwrap()
      setMessage('تم تغيير كلمة المرور بنجاح')
      setMessageType('success')
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
    } catch (error) {
      setMessage('حدث خطأ أثناء تغيير كلمة المرور')
      setMessageType('error')
    } finally {
      setLoading(false)
    }
  }

  const getRoleText = (role) => {
    const roles = {
      'admin': 'مدير النظام',
      'manager': 'مدير فرع',
      'receptionist': 'موظف استقبال',
      'customer': 'عميل'
    }
    return roles[role] || role
  }

  const getRoleColor = (role) => {
    const colors = {
      'admin': 'error',
      'manager': 'warning',
      'receptionist': 'info',
      'customer': 'success'
    }
    return colors[role] || 'default'
  }

  // Sample activity data
  const recentActivity = [
    { id: 1, action: 'تسجيل دخول', date: '2024-01-15 10:30', ip: '***********' },
    { id: 2, action: 'تحديث الملف الشخصي', date: '2024-01-14 15:20', ip: '***********' },
    { id: 3, action: 'إنشاء حجز جديد', date: '2024-01-13 09:15', ip: '***********' },
  ]

  return (
    <Box className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4 space-x-reverse">
        <Avatar
          sx={{
            bgcolor: '#0ea5e9',
            width: 80,
            height: 80,
            fontSize: '2rem'
          }}
        >
          {user?.name?.charAt(0)}
        </Avatar>
        <div>
          <Typography 
            variant="h4" 
            className="font-bold text-gray-800"
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            {user?.name}
          </Typography>
          <div className="flex items-center space-x-2 space-x-reverse mt-2">
            <Chip
              label={getRoleText(user?.role)}
              color={getRoleColor(user?.role)}
              size="small"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            />
            <Typography 
              variant="body2" 
              className="text-gray-600"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              {user?.email}
            </Typography>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Card>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              fontFamily: 'Cairo, sans-serif'
            }
          }}
        >
          <Tab 
            icon={<PersonIcon />} 
            label="المعلومات الشخصية" 
            iconPosition="start"
          />
          <Tab 
            icon={<SecurityIcon />} 
            label="الأمان" 
            iconPosition="start"
          />
          <Tab 
            icon={<HistoryIcon />} 
            label="النشاط الأخير" 
            iconPosition="start"
          />
          <Tab 
            icon={<NotificationsIcon />} 
            label="الإشعارات" 
            iconPosition="start"
          />
        </Tabs>

        <CardContent className="p-6">
          {message && (
            <Alert 
              severity={messageType} 
              className="mb-4"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              {message}
            </Alert>
          )}

          {/* Personal Information Tab */}
          {activeTab === 0 && (
            <form onSubmit={handleProfileSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography 
                    variant="h6" 
                    className="font-semibold mb-4"
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    تحديث المعلومات الشخصية
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    name="name"
                    label="الاسم الكامل"
                    value={profileData.name}
                    onChange={handleProfileChange}
                    InputProps={{
                      startAdornment: <PersonIcon className="text-gray-400 mr-2" />
                    }}
                    sx={{
                      '& .MuiInputLabel-root': {
                        fontFamily: 'Cairo, sans-serif'
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    name="email"
                    type="email"
                    label="البريد الإلكتروني"
                    value={profileData.email}
                    onChange={handleProfileChange}
                    InputProps={{
                      startAdornment: <EmailIcon className="text-gray-400 mr-2" />
                    }}
                    sx={{
                      '& .MuiInputLabel-root': {
                        fontFamily: 'Cairo, sans-serif'
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    name="phone"
                    label="رقم الهاتف"
                    value={profileData.phone}
                    onChange={handleProfileChange}
                    InputProps={{
                      startAdornment: <PhoneIcon className="text-gray-400 mr-2" />
                    }}
                    sx={{
                      '& .MuiInputLabel-root': {
                        fontFamily: 'Cairo, sans-serif'
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Button
                    type="submit"
                    variant="contained"
                    startIcon={<SaveIcon />}
                    disabled={loading}
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    {loading ? 'جاري الحفظ...' : 'حفظ التغييرات'}
                  </Button>
                </Grid>
              </Grid>
            </form>
          )}

          {/* Security Tab */}
          {activeTab === 1 && (
            <form onSubmit={handlePasswordSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography 
                    variant="h6" 
                    className="font-semibold mb-4"
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    تغيير كلمة المرور
                  </Typography>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    name="currentPassword"
                    type="password"
                    label="كلمة المرور الحالية"
                    value={passwordData.currentPassword}
                    onChange={handlePasswordChange}
                    InputProps={{
                      startAdornment: <LockIcon className="text-gray-400 mr-2" />
                    }}
                    sx={{
                      '& .MuiInputLabel-root': {
                        fontFamily: 'Cairo, sans-serif'
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    name="newPassword"
                    type="password"
                    label="كلمة المرور الجديدة"
                    value={passwordData.newPassword}
                    onChange={handlePasswordChange}
                    InputProps={{
                      startAdornment: <LockIcon className="text-gray-400 mr-2" />
                    }}
                    sx={{
                      '& .MuiInputLabel-root': {
                        fontFamily: 'Cairo, sans-serif'
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    name="confirmPassword"
                    type="password"
                    label="تأكيد كلمة المرور الجديدة"
                    value={passwordData.confirmPassword}
                    onChange={handlePasswordChange}
                    InputProps={{
                      startAdornment: <LockIcon className="text-gray-400 mr-2" />
                    }}
                    sx={{
                      '& .MuiInputLabel-root': {
                        fontFamily: 'Cairo, sans-serif'
                      }
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Button
                    type="submit"
                    variant="contained"
                    startIcon={<SaveIcon />}
                    disabled={loading}
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    {loading ? 'جاري التحديث...' : 'تغيير كلمة المرور'}
                  </Button>
                </Grid>
              </Grid>
            </form>
          )}

          {/* Activity Tab */}
          {activeTab === 2 && (
            <Box>
              <Typography 
                variant="h6" 
                className="font-semibold mb-4"
                sx={{ fontFamily: 'Cairo, sans-serif' }}
              >
                النشاط الأخير
              </Typography>
              
              <List>
                {recentActivity.map((activity, index) => (
                  <React.Fragment key={activity.id}>
                    <ListItem>
                      <ListItemIcon>
                        <HistoryIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
                            {activity.action}
                          </Typography>
                        }
                        secondary={
                          <Typography 
                            variant="caption" 
                            className="text-gray-500"
                            sx={{ fontFamily: 'Cairo, sans-serif' }}
                          >
                            {activity.date} • IP: {activity.ip}
                          </Typography>
                        }
                      />
                    </ListItem>
                    {index < recentActivity.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </Box>
          )}

          {/* Notifications Tab */}
          {activeTab === 3 && (
            <Box>
              <Typography 
                variant="h6" 
                className="font-semibold mb-4"
                sx={{ fontFamily: 'Cairo, sans-serif' }}
              >
                إعدادات الإشعارات
              </Typography>
              
              <Typography 
                variant="body2" 
                className="text-gray-600"
                sx={{ fontFamily: 'Cairo, sans-serif' }}
              >
                سيتم إضافة إعدادات الإشعارات قريباً...
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  )
}

export default Profile
