# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=alwaad_alsadiq_4
DB_USER=postgres
DB_PASSWORD=your_database_password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Server Configuration
PORT=5000
NODE_ENV=development

# Frontend Configuration
VITE_API_URL=http://localhost:5000/api

# Email Configuration (Optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-email-password

# File Upload Configuration (Optional)
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5242880

# Redis Configuration (Optional - for caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log
