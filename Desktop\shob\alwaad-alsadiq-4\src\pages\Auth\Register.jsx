import React, { useState, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Link, useNavigate } from 'react-router-dom'
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
  Divider,
  Avatar,
  MenuItem
} from '@mui/material'
import {
  Visibility,
  VisibilityOff,
  Email as EmailIcon,
  Lock as LockIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Business as BusinessIcon
} from '@mui/icons-material'

import { registerUser, clearError } from '../../store/slices/authSlice'
import LoadingSpinner from '../../components/LoadingSpinner/LoadingSpinner'

const Register = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { isLoading, error, isAuthenticated } = useSelector((state) => state.auth)

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    role: 'customer'
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [formErrors, setFormErrors] = useState({})
  const [registrationSuccess, setRegistrationSuccess] = useState(false)

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard')
    }
  }, [isAuthenticated, navigate])

  useEffect(() => {
    return () => {
      dispatch(clearError())
    }
  }, [dispatch])

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = () => {
    const errors = {}

    if (!formData.name) {
      errors.name = 'الاسم مطلوب'
    } else if (formData.name.length < 2) {
      errors.name = 'الاسم يجب أن يكون حرفين على الأقل'
    }

    if (!formData.email) {
      errors.email = 'البريد الإلكتروني مطلوب'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'البريد الإلكتروني غير صحيح'
    }

    if (!formData.phone) {
      errors.phone = 'رقم الهاتف مطلوب'
    } else if (!/^[0-9+\-\s()]+$/.test(formData.phone)) {
      errors.phone = 'رقم الهاتف غير صحيح'
    }

    if (!formData.password) {
      errors.password = 'كلمة المرور مطلوبة'
    } else if (formData.password.length < 6) {
      errors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
    }

    if (!formData.confirmPassword) {
      errors.confirmPassword = 'تأكيد كلمة المرور مطلوب'
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'كلمة المرور غير متطابقة'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    try {
      const { confirmPassword, ...userData } = formData
      await dispatch(registerUser(userData)).unwrap()
      setRegistrationSuccess(true)
      setTimeout(() => {
        navigate('/login')
      }, 2000)
    } catch (error) {
      // Error is handled by Redux
    }
  }

  const handleTogglePassword = () => {
    setShowPassword(!showPassword)
  }

  const handleToggleConfirmPassword = () => {
    setShowConfirmPassword(!showConfirmPassword)
  }

  if (isLoading) {
    return <LoadingSpinner message="جاري إنشاء الحساب..." />
  }

  return (
    <Box 
      className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4"
      dir="rtl"
    >
      <Card 
        className="w-full max-w-md shadow-xl"
        sx={{ 
          borderRadius: '16px',
          overflow: 'visible'
        }}
      >
        <CardContent className="p-8">
          {/* Logo and Title */}
          <Box className="text-center mb-6">
            <Avatar 
              sx={{ 
                bgcolor: '#0ea5e9', 
                width: 64, 
                height: 64,
                margin: '0 auto 16px'
              }}
            >
              <BusinessIcon sx={{ fontSize: 32 }} />
            </Avatar>
            <Typography 
              variant="h5" 
              className="font-bold text-gray-800 mb-2"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              إنشاء حساب جديد
            </Typography>
            <Typography 
              variant="body2" 
              className="text-gray-600"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              انضم إلى نظام إدارة القاعات
            </Typography>
          </Box>

          {/* Success Alert */}
          {registrationSuccess && (
            <Alert 
              severity="success" 
              className="mb-4"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              تم إنشاء الحساب بنجاح! سيتم توجيهك لصفحة تسجيل الدخول...
            </Alert>
          )}

          {/* Error Alert */}
          {error && (
            <Alert 
              severity="error" 
              className="mb-4"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              {error}
            </Alert>
          )}

          {/* Register Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <TextField
              fullWidth
              name="name"
              label="الاسم الكامل"
              value={formData.name}
              onChange={handleChange}
              error={!!formErrors.name}
              helperText={formErrors.name}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PersonIcon className="text-gray-400" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                },
                '& .MuiHelperText-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />

            <TextField
              fullWidth
              name="email"
              type="email"
              label="البريد الإلكتروني"
              value={formData.email}
              onChange={handleChange}
              error={!!formErrors.email}
              helperText={formErrors.email}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <EmailIcon className="text-gray-400" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                },
                '& .MuiHelperText-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />

            <TextField
              fullWidth
              name="phone"
              label="رقم الهاتف"
              value={formData.phone}
              onChange={handleChange}
              error={!!formErrors.phone}
              helperText={formErrors.phone}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PhoneIcon className="text-gray-400" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                },
                '& .MuiHelperText-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />

            <TextField
              fullWidth
              name="password"
              type={showPassword ? 'text' : 'password'}
              label="كلمة المرور"
              value={formData.password}
              onChange={handleChange}
              error={!!formErrors.password}
              helperText={formErrors.password}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LockIcon className="text-gray-400" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={handleTogglePassword}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                },
                '& .MuiHelperText-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />

            <TextField
              fullWidth
              name="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              label="تأكيد كلمة المرور"
              value={formData.confirmPassword}
              onChange={handleChange}
              error={!!formErrors.confirmPassword}
              helperText={formErrors.confirmPassword}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LockIcon className="text-gray-400" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={handleToggleConfirmPassword}
                      edge="end"
                    >
                      {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                },
                '& .MuiHelperText-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              disabled={isLoading || registrationSuccess}
              className="mt-6 py-3"
              sx={{
                fontFamily: 'Cairo, sans-serif',
                fontSize: '16px',
                fontWeight: 600,
                borderRadius: '8px',
                textTransform: 'none'
              }}
            >
              {isLoading ? 'جاري إنشاء الحساب...' : 'إنشاء حساب'}
            </Button>
          </form>

          {/* Divider */}
          <Divider className="my-6" />

          {/* Login Link */}
          <Box className="text-center">
            <Typography 
              variant="body2" 
              className="text-gray-600"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              لديك حساب بالفعل؟{' '}
              <Link 
                to="/login" 
                className="text-primary-600 hover:text-primary-700 font-medium"
              >
                تسجيل الدخول
              </Link>
            </Typography>
          </Box>
        </CardContent>
      </Card>
    </Box>
  )
}

export default Register
