import React, { useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { Snackbar, Alert } from '@mui/material'

// Components
import Layout from './components/Layout/Layout'
import Login from './pages/Auth/Login'
import Register from './pages/Auth/Register'
import Dashboard from './pages/Dashboard/Dashboard'
import Halls from './pages/Halls/Halls'
import Bookings from './pages/Bookings/Bookings'
import Users from './pages/Users/<USER>'
import Reports from './pages/Reports/Reports'
import Profile from './pages/Profile/Profile'
import NotFound from './pages/NotFound/NotFound'

// Redux
import { getCurrentUser } from './store/slices/authSlice'
import { fetchNotifications } from './store/slices/notificationsSlice'

// Utils
import ProtectedRoute from './components/ProtectedRoute/ProtectedRoute'
import LoadingSpinner from './components/LoadingSpinner/LoadingSpinner'

function App() {
  const dispatch = useDispatch()
  const { isAuthenticated, isLoading, user, error } = useSelector((state) => state.auth)
  const [snackbarOpen, setSnackbarOpen] = React.useState(false)

  useEffect(() => {
    const token = localStorage.getItem('token')
    if (token) {
      dispatch(getCurrentUser())
    }
  }, [dispatch])

  useEffect(() => {
    if (isAuthenticated && user) {
      dispatch(fetchNotifications())
    }
  }, [dispatch, isAuthenticated, user])

  useEffect(() => {
    if (error) {
      setSnackbarOpen(true)
    }
  }, [error])

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false)
  }

  if (isLoading) {
    return <LoadingSpinner />
  }

  return (
    <div className="App">
      <Routes>
        {/* Public Routes */}
        <Route 
          path="/login" 
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <Login />
          } 
        />
        <Route 
          path="/register" 
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <Register />
          } 
        />

        {/* Protected Routes */}
        <Route 
          path="/" 
          element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="halls" element={<Halls />} />
          <Route path="bookings" element={<Bookings />} />
          <Route 
            path="users" 
            element={
              <ProtectedRoute requiredRole="admin">
                <Users />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="reports" 
            element={
              <ProtectedRoute requiredRole={["admin", "manager"]}>
                <Reports />
              </ProtectedRoute>
            } 
          />
          <Route path="profile" element={<Profile />} />
        </Route>

        {/* 404 Route */}
        <Route path="*" element={<NotFound />} />
      </Routes>

      {/* Global Snackbar for errors */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity="error" 
          sx={{ width: '100%' }}
        >
          {error}
        </Alert>
      </Snackbar>
    </div>
  )
}

export default App
