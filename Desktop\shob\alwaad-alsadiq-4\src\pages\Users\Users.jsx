import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Dialog,
  Fab,
  TextField,
  MenuItem,
  InputAdornment,
  Avatar,
  Menu
} from '@mui/material'
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreIcon,
  Search as SearchIcon,
  People as PeopleIcon,
  Block as BlockIcon,
  CheckCircle as ActivateIcon
} from '@mui/icons-material'

import { 
  fetchUsers, 
  deleteUser,
  updateUser 
} from '../../store/slices/usersSlice'
import UserForm from '../../components/Users/<USER>'
import ConfirmDialog from '../../components/Common/ConfirmDialog'
import LoadingSpinner from '../../components/LoadingSpinner/LoadingSpinner'

const Users = () => {
  const dispatch = useDispatch()
  const { users, isLoading, pagination } = useSelector((state) => state.users)
  const { user: currentUser } = useSelector((state) => state.auth)

  const [selectedUser, setSelectedUser] = useState(null)
  const [showForm, setShowForm] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [userToDelete, setUserToDelete] = useState(null)
  const [anchorEl, setAnchorEl] = useState(null)
  const [menuUser, setMenuUser] = useState(null)

  // Filters
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')

  useEffect(() => {
    dispatch(fetchUsers({
      page: pagination.page,
      limit: pagination.limit,
      search: searchTerm,
      role: roleFilter !== 'all' ? roleFilter : undefined,
      status: statusFilter !== 'all' ? statusFilter : undefined
    }))
  }, [dispatch, pagination.page, pagination.limit, searchTerm, roleFilter, statusFilter])

  const handleAddUser = () => {
    setSelectedUser(null)
    setShowForm(true)
  }

  const handleEditUser = (user) => {
    setSelectedUser(user)
    setShowForm(true)
    handleCloseMenu()
  }

  const handleDeleteUser = (user) => {
    setUserToDelete(user)
    setShowDeleteDialog(true)
    handleCloseMenu()
  }

  const handleMenuClick = (event, user) => {
    setAnchorEl(event.currentTarget)
    setMenuUser(user)
  }

  const handleCloseMenu = () => {
    setAnchorEl(null)
    setMenuUser(null)
  }

  const handleToggleUserStatus = async (user) => {
    const newStatus = user.status === 'active' ? 'inactive' : 'active'
    await dispatch(updateUser({ 
      id: user.id, 
      userData: { ...user, status: newStatus } 
    }))
    handleCloseMenu()
  }

  const confirmDelete = async () => {
    if (userToDelete) {
      await dispatch(deleteUser(userToDelete.id))
      setShowDeleteDialog(false)
      setUserToDelete(null)
    }
  }

  const getRoleText = (role) => {
    const roles = {
      'admin': 'مدير النظام',
      'manager': 'مدير فرع',
      'receptionist': 'موظف استقبال',
      'customer': 'عميل'
    }
    return roles[role] || role
  }

  const getRoleColor = (role) => {
    const colors = {
      'admin': 'error',
      'manager': 'warning',
      'receptionist': 'info',
      'customer': 'success'
    }
    return colors[role] || 'default'
  }

  const getStatusColor = (status) => {
    return status === 'active' ? 'success' : 'error'
  }

  const getStatusText = (status) => {
    return status === 'active' ? 'نشط' : 'غير نشط'
  }

  const formatDate = (dateString) => {
    try {
      return new Date(dateString).toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    } catch (error) {
      return dateString
    }
  }

  const handlePageChange = (event, newPage) => {
    dispatch(setPagination({ page: newPage + 1 }))
  }

  const handleRowsPerPageChange = (event) => {
    dispatch(setPagination({ 
      page: 1, 
      limit: parseInt(event.target.value, 10) 
    }))
  }

  if (isLoading && users.length === 0) {
    return <LoadingSpinner message="جاري تحميل المستخدمين..." />
  }

  return (
    <Box className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <Typography 
            variant="h4" 
            className="font-bold text-gray-800"
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            إدارة المستخدمين
          </Typography>
          <Typography 
            variant="body2" 
            className="text-gray-600 mt-1"
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            عرض وإدارة جميع مستخدمي النظام
          </Typography>
        </div>
        
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddUser}
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          إضافة مستخدم جديد
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <TextField
              fullWidth
              placeholder="البحث في المستخدمين..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon className="text-gray-400" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiInputBase-input': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />
            
            <TextField
              fullWidth
              select
              label="الدور"
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            >
              <MenuItem value="all">جميع الأدوار</MenuItem>
              <MenuItem value="admin">مدير النظام</MenuItem>
              <MenuItem value="manager">مدير فرع</MenuItem>
              <MenuItem value="receptionist">موظف استقبال</MenuItem>
              <MenuItem value="customer">عميل</MenuItem>
            </TextField>
            
            <TextField
              fullWidth
              select
              label="الحالة"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            >
              <MenuItem value="all">جميع الحالات</MenuItem>
              <MenuItem value="active">نشط</MenuItem>
              <MenuItem value="inactive">غير نشط</MenuItem>
            </TextField>
            
            <div className="flex items-center">
              <Typography 
                variant="body2" 
                className="text-gray-600"
                sx={{ fontFamily: 'Cairo, sans-serif' }}
              >
                {users.length} مستخدم
              </Typography>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                  المستخدم
                </TableCell>
                <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                  البريد الإلكتروني
                </TableCell>
                <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                  الهاتف
                </TableCell>
                <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                  الدور
                </TableCell>
                <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                  الحالة
                </TableCell>
                <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                  تاريخ التسجيل
                </TableCell>
                <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                  الإجراءات
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id} hover>
                  <TableCell>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <Avatar
                        sx={{
                          bgcolor: getRoleColor(user.role) === 'error' ? '#ef4444' :
                                   getRoleColor(user.role) === 'warning' ? '#f59e0b' :
                                   getRoleColor(user.role) === 'info' ? '#3b82f6' : '#10b981',
                          width: 40,
                          height: 40,
                          fontSize: '16px'
                        }}
                      >
                        {user.name?.charAt(0)}
                      </Avatar>
                      <div>
                        <Typography
                          variant="body2"
                          className="font-medium"
                          sx={{ fontFamily: 'Cairo, sans-serif' }}
                        >
                          {user.name}
                        </Typography>
                        <Typography
                          variant="caption"
                          className="text-gray-500"
                          sx={{ fontFamily: 'Cairo, sans-serif' }}
                        >
                          ID: {user.id}
                        </Typography>
                      </div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <Typography
                      variant="body2"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {user.email}
                    </Typography>
                  </TableCell>
                  
                  <TableCell>
                    <Typography
                      variant="body2"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {user.phone || 'غير محدد'}
                    </Typography>
                  </TableCell>
                  
                  <TableCell>
                    <Chip
                      label={getRoleText(user.role)}
                      color={getRoleColor(user.role)}
                      size="small"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    />
                  </TableCell>
                  
                  <TableCell>
                    <Chip
                      label={getStatusText(user.status)}
                      color={getStatusColor(user.status)}
                      size="small"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    />
                  </TableCell>
                  
                  <TableCell>
                    <Typography
                      variant="body2"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {formatDate(user.created_at)}
                    </Typography>
                  </TableCell>
                  
                  <TableCell>
                    <IconButton
                      onClick={(e) => handleMenuClick(e, user)}
                      disabled={user.id === currentUser?.id}
                    >
                      <MoreIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        
        <TablePagination
          component="div"
          count={pagination.total}
          page={pagination.page - 1}
          onPageChange={handlePageChange}
          rowsPerPage={pagination.limit}
          onRowsPerPageChange={handleRowsPerPageChange}
          rowsPerPageOptions={[5, 10, 25, 50]}
          labelRowsPerPage="عدد الصفوف في الصفحة:"
          labelDisplayedRows={({ from, to, count }) => 
            `${from}-${to} من ${count !== -1 ? count : `أكثر من ${to}`}`
          }
          sx={{
            '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
              fontFamily: 'Cairo, sans-serif'
            }
          }}
        />
      </Card>

      {/* Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
      >
        <MenuItem onClick={() => handleEditUser(menuUser)}>
          <EditIcon className="mr-2" fontSize="small" />
          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
            تعديل
          </Typography>
        </MenuItem>
        
        <MenuItem onClick={() => handleToggleUserStatus(menuUser)}>
          {menuUser?.status === 'active' ? (
            <>
              <BlockIcon className="mr-2" fontSize="small" />
              <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
                إلغاء التفعيل
              </Typography>
            </>
          ) : (
            <>
              <ActivateIcon className="mr-2" fontSize="small" />
              <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
                تفعيل
              </Typography>
            </>
          )}
        </MenuItem>
        
        <MenuItem 
          onClick={() => handleDeleteUser(menuUser)}
          disabled={menuUser?.role === 'admin'}
        >
          <DeleteIcon className="mr-2" fontSize="small" />
          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
            حذف
          </Typography>
        </MenuItem>
      </Menu>

      {/* Floating Action Button for Mobile */}
      <Fab
        color="primary"
        aria-label="add user"
        onClick={handleAddUser}
        sx={{
          position: 'fixed',
          bottom: 16,
          left: 16,
          display: { xs: 'flex', md: 'none' }
        }}
      >
        <AddIcon />
      </Fab>

      {/* User Form Dialog */}
      <Dialog
        open={showForm}
        onClose={() => setShowForm(false)}
        maxWidth="md"
        fullWidth
      >
        <UserForm
          user={selectedUser}
          onClose={() => setShowForm(false)}
          onSuccess={() => {
            setShowForm(false)
            dispatch(fetchUsers())
          }}
        />
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={showDeleteDialog}
        title="حذف المستخدم"
        message={`هل أنت متأكد من حذف المستخدم "${userToDelete?.name}"؟ هذا الإجراء لا يمكن التراجع عنه.`}
        onConfirm={confirmDelete}
        onCancel={() => {
          setShowDeleteDialog(false)
          setUserToDelete(null)
        }}
        confirmText="حذف"
        cancelText="إلغاء"
        severity="error"
      />
    </Box>
  )
}

export default Users
