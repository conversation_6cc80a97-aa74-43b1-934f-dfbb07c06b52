import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import axios from 'axios'

const API_URL = '/api/reports'

// Async thunks
export const fetchDashboardStats = createAsyncThunk(
  'reports/fetchDashboardStats',
  async (_, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.get(`${API_URL}/dashboard`, {
        headers: { Authorization: `Bearer ${token}` }
      })
      return response.data
    } catch (error) {
      return rejectWithValue(error.response.data.message)
    }
  }
)

export const fetchRevenueReport = createAsyncThunk(
  'reports/fetchRevenueReport',
  async (params, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token')
      const queryParams = new URLSearchParams(params).toString()
      const response = await axios.get(`${API_URL}/revenue?${queryParams}`, {
        headers: { Authorization: `Bearer ${token}` }
      })
      return response.data
    } catch (error) {
      return rejectWithValue(error.response.data.message)
    }
  }
)

export const fetchBookingsReport = createAsyncThunk(
  'reports/fetchBookingsReport',
  async (params, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token')
      const queryParams = new URLSearchParams(params).toString()
      const response = await axios.get(`${API_URL}/bookings?${queryParams}`, {
        headers: { Authorization: `Bearer ${token}` }
      })
      return response.data
    } catch (error) {
      return rejectWithValue(error.response.data.message)
    }
  }
)

export const fetchHallsReport = createAsyncThunk(
  'reports/fetchHallsReport',
  async (params, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token')
      const queryParams = new URLSearchParams(params).toString()
      const response = await axios.get(`${API_URL}/halls?${queryParams}`, {
        headers: { Authorization: `Bearer ${token}` }
      })
      return response.data
    } catch (error) {
      return rejectWithValue(error.response.data.message)
    }
  }
)

export const exportReport = createAsyncThunk(
  'reports/exportReport',
  async ({ type, params, format }, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token')
      const queryParams = new URLSearchParams({ ...params, format }).toString()
      const response = await axios.get(`${API_URL}/export/${type}?${queryParams}`, {
        headers: { Authorization: `Bearer ${token}` },
        responseType: 'blob'
      })
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `${type}_report.${format}`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      
      return true
    } catch (error) {
      return rejectWithValue('خطأ في تصدير التقرير')
    }
  }
)

const initialState = {
  dashboardStats: {
    totalBookings: 0,
    totalRevenue: 0,
    totalHalls: 0,
    totalCustomers: 0,
    todayBookings: 0,
    monthlyRevenue: 0,
    occupancyRate: 0,
    popularHalls: []
  },
  revenueReport: {
    data: [],
    summary: {}
  },
  bookingsReport: {
    data: [],
    summary: {}
  },
  hallsReport: {
    data: [],
    summary: {}
  },
  isLoading: false,
  error: null,
  dateRange: {
    from: null,
    to: null
  }
}

const reportsSlice = createSlice({
  name: 'reports',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setDateRange: (state, action) => {
      state.dateRange = action.payload
    },
    clearReports: (state) => {
      state.revenueReport = { data: [], summary: {} }
      state.bookingsReport = { data: [], summary: {} }
      state.hallsReport = { data: [], summary: {} }
    }
  },
  extraReducers: (builder) => {
    builder
      // Dashboard stats
      .addCase(fetchDashboardStats.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchDashboardStats.fulfilled, (state, action) => {
        state.isLoading = false
        state.dashboardStats = action.payload
        state.error = null
      })
      .addCase(fetchDashboardStats.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
      
      // Revenue report
      .addCase(fetchRevenueReport.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchRevenueReport.fulfilled, (state, action) => {
        state.isLoading = false
        state.revenueReport = action.payload
        state.error = null
      })
      .addCase(fetchRevenueReport.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
      
      // Bookings report
      .addCase(fetchBookingsReport.fulfilled, (state, action) => {
        state.bookingsReport = action.payload
      })
      
      // Halls report
      .addCase(fetchHallsReport.fulfilled, (state, action) => {
        state.hallsReport = action.payload
      })
      
      // Export report
      .addCase(exportReport.pending, (state) => {
        state.isLoading = true
      })
      .addCase(exportReport.fulfilled, (state) => {
        state.isLoading = false
      })
      .addCase(exportReport.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
  },
})

export const { clearError, setDateRange, clearReports } = reportsSlice.actions
export default reportsSlice.reducer
