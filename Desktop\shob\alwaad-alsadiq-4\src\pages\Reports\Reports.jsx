import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  TextField,
  MenuItem,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip
} from '@mui/material'
import {
  Download as DownloadIcon,
  Assessment as ReportsIcon,
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  EventNote as BookingsIcon,
  MeetingRoom as HallsIcon
} from '@mui/icons-material'

import { 
  fetchRevenueReport,
  fetchBookingsReport,
  fetchHallsReport,
  exportReport,
  setDateRange
} from '../../store/slices/reportsSlice'
import RevenueChart from '../../components/Dashboard/RevenueChart'
import LoadingSpinner from '../../components/LoadingSpinner/LoadingSpinner'

const Reports = () => {
  const dispatch = useDispatch()
  const { 
    revenueReport, 
    bookingsReport, 
    hallsReport, 
    isLoading, 
    dateRange 
  } = useSelector((state) => state.reports)

  const [activeTab, setActiveTab] = useState(0)
  const [reportType, setReportType] = useState('monthly')
  const [exportFormat, setExportFormat] = useState('pdf')
  const [dateFrom, setDateFrom] = useState('')
  const [dateTo, setDateTo] = useState('')

  useEffect(() => {
    // Set default date range (last 30 days)
    const today = new Date()
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate())
    
    setDateFrom(lastMonth.toISOString().split('T')[0])
    setDateTo(today.toISOString().split('T')[0])
  }, [])

  useEffect(() => {
    if (dateFrom && dateTo) {
      const params = {
        from: dateFrom,
        to: dateTo,
        type: reportType
      }

      dispatch(setDateRange({ from: dateFrom, to: dateTo }))
      
      switch (activeTab) {
        case 0:
          dispatch(fetchRevenueReport(params))
          break
        case 1:
          dispatch(fetchBookingsReport(params))
          break
        case 2:
          dispatch(fetchHallsReport(params))
          break
        default:
          break
      }
    }
  }, [dispatch, activeTab, dateFrom, dateTo, reportType])

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue)
  }

  const handleExport = () => {
    const reportTypes = ['revenue', 'bookings', 'halls']
    const currentReportType = reportTypes[activeTab]
    
    dispatch(exportReport({
      type: currentReportType,
      params: {
        from: dateFrom,
        to: dateTo,
        type: reportType
      },
      format: exportFormat
    }))
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  const formatDate = (dateString) => {
    try {
      return new Date(dateString).toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    } catch (error) {
      return dateString
    }
  }

  if (isLoading) {
    return <LoadingSpinner message="جاري تحميل التقارير..." />
  }

  return (
    <Box className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <Typography 
            variant="h4" 
            className="font-bold text-gray-800"
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            التقارير والإحصائيات
          </Typography>
          <Typography 
            variant="body2" 
            className="text-gray-600 mt-1"
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            عرض وتحليل بيانات النظام
          </Typography>
        </div>
        
        <Button
          variant="contained"
          startIcon={<DownloadIcon />}
          onClick={handleExport}
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          تصدير التقرير
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                type="date"
                label="من تاريخ"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
                InputLabelProps={{ shrink: true }}
                sx={{
                  '& .MuiInputLabel-root': {
                    fontFamily: 'Cairo, sans-serif'
                  }
                }}
              />
            </Grid>
            
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                type="date"
                label="إلى تاريخ"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
                InputLabelProps={{ shrink: true }}
                sx={{
                  '& .MuiInputLabel-root': {
                    fontFamily: 'Cairo, sans-serif'
                  }
                }}
              />
            </Grid>
            
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                select
                label="نوع التقرير"
                value={reportType}
                onChange={(e) => setReportType(e.target.value)}
                sx={{
                  '& .MuiInputLabel-root': {
                    fontFamily: 'Cairo, sans-serif'
                  }
                }}
              >
                <MenuItem value="daily">يومي</MenuItem>
                <MenuItem value="weekly">أسبوعي</MenuItem>
                <MenuItem value="monthly">شهري</MenuItem>
                <MenuItem value="yearly">سنوي</MenuItem>
              </TextField>
            </Grid>
            
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                select
                label="تنسيق التصدير"
                value={exportFormat}
                onChange={(e) => setExportFormat(e.target.value)}
                sx={{
                  '& .MuiInputLabel-root': {
                    fontFamily: 'Cairo, sans-serif'
                  }
                }}
              >
                <MenuItem value="pdf">PDF</MenuItem>
                <MenuItem value="excel">Excel</MenuItem>
                <MenuItem value="csv">CSV</MenuItem>
              </TextField>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Report Tabs */}
      <Card>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              fontFamily: 'Cairo, sans-serif'
            }
          }}
        >
          <Tab 
            icon={<MoneyIcon />} 
            label="تقرير الإيرادات" 
            iconPosition="start"
          />
          <Tab 
            icon={<BookingsIcon />} 
            label="تقرير الحجوزات" 
            iconPosition="start"
          />
          <Tab 
            icon={<HallsIcon />} 
            label="تقرير القاعات" 
            iconPosition="start"
          />
        </Tabs>

        <CardContent className="p-6">
          {/* Revenue Report Tab */}
          {activeTab === 0 && (
            <Box className="space-y-6">
              <Typography 
                variant="h6" 
                className="font-semibold"
                sx={{ fontFamily: 'Cairo, sans-serif' }}
              >
                تقرير الإيرادات
              </Typography>

              {/* Revenue Summary */}
              <Grid container spacing={3}>
                <Grid item xs={12} md={3}>
                  <Card className="bg-green-50 border border-green-200">
                    <CardContent className="text-center">
                      <Typography 
                        variant="h4" 
                        className="font-bold text-green-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        {formatCurrency(revenueReport.summary?.total || 0)}
                      </Typography>
                      <Typography 
                        variant="body2" 
                        className="text-green-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        إجمالي الإيرادات
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                
                <Grid item xs={12} md={3}>
                  <Card className="bg-blue-50 border border-blue-200">
                    <CardContent className="text-center">
                      <Typography 
                        variant="h4" 
                        className="font-bold text-blue-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        {revenueReport.summary?.bookings || 0}
                      </Typography>
                      <Typography 
                        variant="body2" 
                        className="text-blue-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        عدد الحجوزات
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                
                <Grid item xs={12} md={3}>
                  <Card className="bg-purple-50 border border-purple-200">
                    <CardContent className="text-center">
                      <Typography 
                        variant="h4" 
                        className="font-bold text-purple-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        {formatCurrency(revenueReport.summary?.average || 0)}
                      </Typography>
                      <Typography 
                        variant="body2" 
                        className="text-purple-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        متوسط الحجز
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                
                <Grid item xs={12} md={3}>
                  <Card className="bg-orange-50 border border-orange-200">
                    <CardContent className="text-center">
                      <Typography 
                        variant="h4" 
                        className="font-bold text-orange-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        {revenueReport.summary?.growth || 0}%
                      </Typography>
                      <Typography 
                        variant="body2" 
                        className="text-orange-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        نمو الإيرادات
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* Revenue Chart */}
              <Card>
                <CardContent>
                  <Typography 
                    variant="h6" 
                    className="font-semibold mb-4"
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    الإيرادات خلال الفترة
                  </Typography>
                  <RevenueChart />
                </CardContent>
              </Card>
            </Box>
          )}

          {/* Bookings Report Tab */}
          {activeTab === 1 && (
            <Box className="space-y-6">
              <Typography 
                variant="h6" 
                className="font-semibold"
                sx={{ fontFamily: 'Cairo, sans-serif' }}
              >
                تقرير الحجوزات
              </Typography>

              {/* Bookings Table */}
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                        التاريخ
                      </TableCell>
                      <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                        عدد الحجوزات
                      </TableCell>
                      <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                        الحجوزات المؤكدة
                      </TableCell>
                      <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                        الحجوزات الملغية
                      </TableCell>
                      <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                        الإيرادات
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {(bookingsReport.data || []).map((row, index) => (
                      <TableRow key={index}>
                        <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>
                          {formatDate(row.date)}
                        </TableCell>
                        <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>
                          {row.total || 0}
                        </TableCell>
                        <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>
                          <Chip 
                            label={row.confirmed || 0} 
                            color="success" 
                            size="small"
                            sx={{ fontFamily: 'Cairo, sans-serif' }}
                          />
                        </TableCell>
                        <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>
                          <Chip 
                            label={row.cancelled || 0} 
                            color="error" 
                            size="small"
                            sx={{ fontFamily: 'Cairo, sans-serif' }}
                          />
                        </TableCell>
                        <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>
                          {formatCurrency(row.revenue || 0)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}

          {/* Halls Report Tab */}
          {activeTab === 2 && (
            <Box className="space-y-6">
              <Typography 
                variant="h6" 
                className="font-semibold"
                sx={{ fontFamily: 'Cairo, sans-serif' }}
              >
                تقرير القاعات
              </Typography>

              {/* Halls Performance Table */}
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                        اسم القاعة
                      </TableCell>
                      <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                        عدد الحجوزات
                      </TableCell>
                      <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                        ساعات الاستخدام
                      </TableCell>
                      <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                        معدل الإشغال
                      </TableCell>
                      <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                        الإيرادات
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {(hallsReport.data || []).map((hall, index) => (
                      <TableRow key={index}>
                        <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>
                          {hall.name || `قاعة ${index + 1}`}
                        </TableCell>
                        <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>
                          {hall.bookings || 0}
                        </TableCell>
                        <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>
                          {hall.hours || 0} ساعة
                        </TableCell>
                        <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>
                          <Chip 
                            label={`${hall.occupancy || 0}%`}
                            color={
                              (hall.occupancy || 0) >= 80 ? 'error' :
                              (hall.occupancy || 0) >= 60 ? 'warning' : 'success'
                            }
                            size="small"
                            sx={{ fontFamily: 'Cairo, sans-serif' }}
                          />
                        </TableCell>
                        <TableCell sx={{ fontFamily: 'Cairo, sans-serif' }}>
                          {formatCurrency(hall.revenue || 0)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  )
}

export default Reports
