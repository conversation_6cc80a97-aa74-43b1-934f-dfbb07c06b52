import React, { useState, useEffect } from 'react'
import { useDispatch } from 'react-redux'
import {
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  MenuItem,
  FormControlLabel,
  Switch,
  Typography,
  Box,
  Alert,
  Chip,
  IconButton
} from '@mui/material'
import {
  Close as CloseIcon,
  Add as AddIcon,
  Remove as RemoveIcon
} from '@mui/icons-material'

import { createHall, updateHall } from '../../store/slices/hallsSlice'

const HallForm = ({ hall, onClose, onSuccess }) => {
  const dispatch = useDispatch()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'general',
    capacity: '',
    pricePerHour: '',
    location: '',
    amenities: [],
    status: 'active',
    images: [],
    workingHours: {
      start: '08:00',
      end: '23:00'
    },
    rules: []
  })

  const [newAmenity, setNewAmenity] = useState('')
  const [newRule, setNewRule] = useState('')

  useEffect(() => {
    if (hall) {
      setFormData({
        name: hall.name || '',
        description: hall.description || '',
        type: hall.type || 'general',
        capacity: hall.capacity || '',
        pricePerHour: hall.pricePerHour || '',
        location: hall.location || '',
        amenities: hall.amenities || [],
        status: hall.status || 'active',
        images: hall.images || [],
        workingHours: hall.workingHours || { start: '08:00', end: '23:00' },
        rules: hall.rules || []
      })
    }
  }, [hall])

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.')
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }))
    }
  }

  const handleAddAmenity = () => {
    if (newAmenity.trim()) {
      setFormData(prev => ({
        ...prev,
        amenities: [...prev.amenities, newAmenity.trim()]
      }))
      setNewAmenity('')
    }
  }

  const handleRemoveAmenity = (index) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.filter((_, i) => i !== index)
    }))
  }

  const handleAddRule = () => {
    if (newRule.trim()) {
      setFormData(prev => ({
        ...prev,
        rules: [...prev.rules, newRule.trim()]
      }))
      setNewRule('')
    }
  }

  const handleRemoveRule = (index) => {
    setFormData(prev => ({
      ...prev,
      rules: prev.rules.filter((_, i) => i !== index)
    }))
  }

  const validateForm = () => {
    if (!formData.name.trim()) {
      setError('اسم القاعة مطلوب')
      return false
    }
    if (!formData.capacity || formData.capacity <= 0) {
      setError('السعة يجب أن تكون أكبر من صفر')
      return false
    }
    if (!formData.pricePerHour || formData.pricePerHour <= 0) {
      setError('السعر يجب أن يكون أكبر من صفر')
      return false
    }
    return true
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      const hallData = {
        ...formData,
        capacity: parseInt(formData.capacity),
        pricePerHour: parseFloat(formData.pricePerHour)
      }

      if (hall) {
        await dispatch(updateHall({ id: hall.id, hallData })).unwrap()
      } else {
        await dispatch(createHall(hallData)).unwrap()
      }
      
      onSuccess()
    } catch (error) {
      setError(error.message || 'حدث خطأ أثناء حفظ البيانات')
    } finally {
      setLoading(false)
    }
  }

  const hallTypes = [
    { value: 'vip', label: 'VIP' },
    { value: 'meeting', label: 'اجتماعات' },
    { value: 'conference', label: 'مؤتمرات' },
    { value: 'wedding', label: 'أفراح' },
    { value: 'gaming', label: 'ألعاب' },
    { value: 'general', label: 'عامة' }
  ]

  return (
    <form onSubmit={handleSubmit}>
      <DialogTitle className="flex justify-between items-center">
        <Typography 
          variant="h6" 
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          {hall ? 'تعديل القاعة' : 'إضافة قاعة جديدة'}
        </Typography>
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers className="space-y-4">
        {error && (
          <Alert severity="error" sx={{ fontFamily: 'Cairo, sans-serif' }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <Typography 
              variant="subtitle1" 
              className="font-semibold mb-3"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              المعلومات الأساسية
            </Typography>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              required
              name="name"
              label="اسم القاعة"
              value={formData.name}
              onChange={handleChange}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              select
              name="type"
              label="نوع القاعة"
              value={formData.type}
              onChange={handleChange}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            >
              {hallTypes.map((type) => (
                <MenuItem key={type.value} value={type.value}>
                  {type.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              multiline
              rows={3}
              name="description"
              label="وصف القاعة"
              value={formData.description}
              onChange={handleChange}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              required
              type="number"
              name="capacity"
              label="السعة (عدد الأشخاص)"
              value={formData.capacity}
              onChange={handleChange}
              inputProps={{ min: 1 }}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              required
              type="number"
              name="pricePerHour"
              label="السعر لكل ساعة (ر.س)"
              value={formData.pricePerHour}
              onChange={handleChange}
              inputProps={{ min: 0, step: 0.01 }}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              name="location"
              label="الموقع"
              value={formData.location}
              onChange={handleChange}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />
          </Grid>

          {/* Working Hours */}
          <Grid item xs={12}>
            <Typography 
              variant="subtitle1" 
              className="font-semibold mb-3"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              ساعات العمل
            </Typography>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              type="time"
              name="workingHours.start"
              label="وقت البداية"
              value={formData.workingHours.start}
              onChange={handleChange}
              InputLabelProps={{ shrink: true }}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              type="time"
              name="workingHours.end"
              label="وقت النهاية"
              value={formData.workingHours.end}
              onChange={handleChange}
              InputLabelProps={{ shrink: true }}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />
          </Grid>

          {/* Amenities */}
          <Grid item xs={12}>
            <Typography 
              variant="subtitle1" 
              className="font-semibold mb-3"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              المرافق والخدمات
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <div className="flex space-x-2 space-x-reverse mb-3">
              <TextField
                fullWidth
                placeholder="إضافة مرفق جديد..."
                value={newAmenity}
                onChange={(e) => setNewAmenity(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleAddAmenity()}
                sx={{
                  '& .MuiInputBase-input': {
                    fontFamily: 'Cairo, sans-serif'
                  }
                }}
              />
              <Button
                variant="outlined"
                onClick={handleAddAmenity}
                disabled={!newAmenity.trim()}
                sx={{ minWidth: 'auto', px: 2 }}
              >
                <AddIcon />
              </Button>
            </div>
            
            <Box className="flex flex-wrap gap-2">
              {formData.amenities.map((amenity, index) => (
                <Chip
                  key={index}
                  label={amenity}
                  onDelete={() => handleRemoveAmenity(index)}
                  color="primary"
                  variant="outlined"
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                />
              ))}
            </Box>
          </Grid>

          {/* Rules */}
          <Grid item xs={12}>
            <Typography 
              variant="subtitle1" 
              className="font-semibold mb-3"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              قوانين وشروط الاستخدام
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <div className="flex space-x-2 space-x-reverse mb-3">
              <TextField
                fullWidth
                placeholder="إضافة قانون جديد..."
                value={newRule}
                onChange={(e) => setNewRule(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleAddRule()}
                sx={{
                  '& .MuiInputBase-input': {
                    fontFamily: 'Cairo, sans-serif'
                  }
                }}
              />
              <Button
                variant="outlined"
                onClick={handleAddRule}
                disabled={!newRule.trim()}
                sx={{ minWidth: 'auto', px: 2 }}
              >
                <AddIcon />
              </Button>
            </div>
            
            <Box className="space-y-2">
              {formData.rules.map((rule, index) => (
                <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                  <Typography 
                    variant="body2"
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    {rule}
                  </Typography>
                  <IconButton
                    size="small"
                    onClick={() => handleRemoveRule(index)}
                    color="error"
                  >
                    <RemoveIcon fontSize="small" />
                  </IconButton>
                </div>
              ))}
            </Box>
          </Grid>

          {/* Status */}
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.status === 'active'}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    status: e.target.checked ? 'active' : 'inactive'
                  }))}
                />
              }
              label={
                <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
                  القاعة متاحة للحجز
                </Typography>
              }
            />
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions className="p-4">
        <Button 
          onClick={onClose}
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          إلغاء
        </Button>
        <Button
          type="submit"
          variant="contained"
          disabled={loading}
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          {loading ? 'جاري الحفظ...' : (hall ? 'تحديث' : 'إضافة')}
        </Button>
      </DialogActions>
    </form>
  )
}

export default HallForm
