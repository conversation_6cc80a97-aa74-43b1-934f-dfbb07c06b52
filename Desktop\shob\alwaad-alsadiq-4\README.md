# نظام إدارة قاعات الوعد الصادق 4

نظام شامل لإدارة قاعات الاجتماعات والفعاليات مع واجهة عربية حديثة ومميزات متقدمة.

## المميزات الرئيسية

### 🏢 إدارة القاعات
- إضافة وتعديل وحذف القاعات
- أنواع مختلفة من القاعات (VIP، اجتماعات، مؤتمرات، أفراح، ألعاب)
- إدارة المرافق والخدمات
- تحديد ساعات العمل والقوانين
- نظام تسعير مرن

### 📅 نظام الحجوزات
- حجز القاعات بسهولة
- تقويم تفاعلي لعرض المواعيد
- إدارة حالات الحجز (في الانتظار، مؤكد، مكتمل، ملغي)
- حساب التكلفة تلقائياً
- إشعارات الحجوزات

### 👥 إدارة المستخدمين
- أدوار مختلفة (مدير، مدير فرع، موظف استقبال، عميل)
- نظام صلاحيات متقدم
- ملفات شخصية للمستخدمين
- تسجيل دخول آمن

### 📊 التقارير والإحصائيات
- لوحة تحكم شاملة
- تقارير الإيرادات
- إحصائيات الحجوزات
- معدلات الإشغال
- تصدير التقارير

### 🔔 نظام الإشعارات
- إشعارات فورية للحجوزات الجديدة
- تنبيهات المواعيد
- إشعارات الدفع
- إشعارات النظام

## التقنيات المستخدمة

### Frontend
- **React 18** - مكتبة واجهة المستخدم
- **Vite** - أداة البناء السريعة
- **Material-UI** - مكونات واجهة المستخدم
- **Tailwind CSS** - إطار عمل CSS
- **Redux Toolkit** - إدارة الحالة
- **React Router** - التنقل بين الصفحات
- **Recharts** - الرسوم البيانية
- **Axios** - طلبات HTTP

### Backend
- **Node.js** - بيئة تشغيل JavaScript
- **Express.js** - إطار عمل الخادم
- **PostgreSQL** - قاعدة البيانات
- **JWT** - المصادقة والتفويض
- **bcryptjs** - تشفير كلمات المرور

## متطلبات النظام

- Node.js (الإصدار 16 أو أحدث)
- PostgreSQL (الإصدار 12 أو أحدث)
- npm أو yarn

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd alwaad-alsadiq-4
```

### 2. تثبيت المكتبات
```bash
npm install
```

### 3. إعداد قاعدة البيانات
1. تأكد من تشغيل PostgreSQL
2. أنشئ قاعدة بيانات جديدة:
```sql
CREATE DATABASE alwaad_alsadiq_4;
```

### 4. إعداد متغيرات البيئة
أنشئ ملف `.env` في المجلد الجذر:
```env
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=alwaad_alsadiq_4
DB_USER=postgres
DB_PASSWORD=your_password

# JWT
JWT_SECRET=your-super-secret-jwt-key

# Server
PORT=5000
```

### 5. تشغيل الخادم
```bash
npm run server
```

### 6. تشغيل واجهة المستخدم
في terminal جديد:
```bash
npm run dev
```

### 7. الوصول للنظام
- واجهة المستخدم: http://localhost:3000
- API الخادم: http://localhost:5000

## بيانات الدخول الافتراضية

### المدير
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `admin123`

## هيكل المشروع

```
alwaad-alsadiq-4/
├── public/                 # الملفات العامة
├── src/                    # كود واجهة المستخدم
│   ├── components/         # المكونات المشتركة
│   ├── pages/             # صفحات التطبيق
│   ├── store/             # إدارة الحالة (Redux)
│   ├── utils/             # الأدوات المساعدة
│   └── styles/            # ملفات التنسيق
├── server/                # كود الخادم
│   ├── routes/            # مسارات API
│   ├── middleware/        # الوسطاء
│   ├── models/            # نماذج البيانات
│   └── utils/             # الأدوات المساعدة
└── docs/                  # الوثائق
```

## الأدوار والصلاحيات

### مدير النظام (Admin)
- الوصول الكامل لجميع المميزات
- إدارة المستخدمين والأدوار
- إعدادات النظام
- جميع التقارير

### مدير الفرع (Manager)
- إدارة القاعات والحجوزات
- التقارير والإحصائيات
- إدارة موظفي الاستقبال

### موظف الاستقبال (Receptionist)
- إدارة الحجوزات
- عرض القاعات
- خدمة العملاء

### العميل (Customer)
- عرض القاعات المتاحة
- إنشاء حجوزات جديدة
- متابعة الحجوزات الشخصية

## المساهمة في المشروع

1. Fork المشروع
2. أنشئ branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للـ branch (`git push origin feature/amazing-feature`)
5. افتح Pull Request

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- افتح issue في GitHub
- راسلنا على البريد الإلكتروني

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الشكر والتقدير

شكر خاص لجميع المساهمين في تطوير هذا النظام وجعله أفضل.

---

© 2024 الوعد الصادق 4 - جميع الحقوق محفوظة
