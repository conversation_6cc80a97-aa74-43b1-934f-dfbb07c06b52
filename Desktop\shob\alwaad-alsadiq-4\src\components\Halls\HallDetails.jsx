import React from 'react'
import { useSelector } from 'react-redux'
import {
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Chip,
  Grid,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Divider
} from '@mui/material'
import {
  Close as CloseIcon,
  Edit as EditIcon,
  MeetingRoom as HallIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  LocationOn as LocationIcon,
  Schedule as ScheduleIcon,
  Star as StarIcon,
  CheckCircle as CheckIcon,
  Rule as RuleIcon
} from '@mui/icons-material'

const HallDetails = ({ hall, onClose, onEdit }) => {
  const { user } = useSelector((state) => state.auth)
  const canEdit = ['admin', 'manager'].includes(user?.role)

  if (!hall) {
    return null
  }

  const getHallTypeText = (type) => {
    const types = {
      'vip': 'VIP',
      'meeting': 'اجتماعات',
      'conference': 'مؤتمرات',
      'wedding': 'أفراح',
      'gaming': 'ألعاب',
      'general': 'عامة'
    }
    return types[type] || type
  }

  const getHallTypeColor = (type) => {
    const colors = {
      'vip': 'secondary',
      'meeting': 'primary',
      'conference': 'info',
      'wedding': 'error',
      'gaming': 'warning',
      'general': 'default'
    }
    return colors[type] || 'default'
  }

  const formatTime = (time) => {
    try {
      return new Date(`2000-01-01T${time}`).toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      })
    } catch (error) {
      return time
    }
  }

  return (
    <>
      <DialogTitle className="flex justify-between items-center">
        <div className="flex items-center space-x-3 space-x-reverse">
          <HallIcon color="primary" />
          <div>
            <Typography 
              variant="h6" 
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              {hall.name}
            </Typography>
            <Chip
              label={getHallTypeText(hall.type)}
              color={getHallTypeColor(hall.type)}
              size="small"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            />
          </div>
        </div>
        <div className="flex items-center space-x-2 space-x-reverse">
          {canEdit && (
            <IconButton onClick={onEdit} color="primary">
              <EditIcon />
            </IconButton>
          )}
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </div>
      </DialogTitle>

      <DialogContent dividers>
        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardContent>
                <Typography 
                  variant="h6" 
                  className="font-semibold mb-3"
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  المعلومات الأساسية
                </Typography>
                
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Box className="flex items-center space-x-2 space-x-reverse mb-2">
                      <PeopleIcon color="action" fontSize="small" />
                      <Typography 
                        variant="body2" 
                        className="text-gray-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        السعة:
                      </Typography>
                      <Typography 
                        variant="body2" 
                        className="font-medium"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        {hall.capacity} شخص
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <Box className="flex items-center space-x-2 space-x-reverse mb-2">
                      <MoneyIcon color="action" fontSize="small" />
                      <Typography 
                        variant="body2" 
                        className="text-gray-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        السعر:
                      </Typography>
                      <Typography 
                        variant="body2" 
                        className="font-medium"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        {hall.pricePerHour} ر.س/ساعة
                      </Typography>
                    </Box>
                  </Grid>
                  
                  {hall.location && (
                    <Grid item xs={12}>
                      <Box className="flex items-center space-x-2 space-x-reverse mb-2">
                        <LocationIcon color="action" fontSize="small" />
                        <Typography 
                          variant="body2" 
                          className="text-gray-600"
                          sx={{ fontFamily: 'Cairo, sans-serif' }}
                        >
                          الموقع:
                        </Typography>
                        <Typography 
                          variant="body2" 
                          className="font-medium"
                          sx={{ fontFamily: 'Cairo, sans-serif' }}
                        >
                          {hall.location}
                        </Typography>
                      </Box>
                    </Grid>
                  )}
                  
                  <Grid item xs={12}>
                    <Box className="flex items-center space-x-2 space-x-reverse mb-2">
                      <ScheduleIcon color="action" fontSize="small" />
                      <Typography 
                        variant="body2" 
                        className="text-gray-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        ساعات العمل:
                      </Typography>
                      <Typography 
                        variant="body2" 
                        className="font-medium"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        {formatTime(hall.workingHours?.start || '08:00')} - {formatTime(hall.workingHours?.end || '23:00')}
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Box className="flex items-center space-x-2 space-x-reverse">
                      <Typography 
                        variant="body2" 
                        className="text-gray-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        الحالة:
                      </Typography>
                      <Chip
                        label={hall.status === 'active' ? 'متاحة' : 'غير متاحة'}
                        color={hall.status === 'active' ? 'success' : 'error'}
                        size="small"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      />
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Description */}
          {hall.description && (
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography 
                    variant="h6" 
                    className="font-semibold mb-3"
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    الوصف
                  </Typography>
                  <Typography 
                    variant="body2" 
                    className="text-gray-700 leading-relaxed"
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    {hall.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          )}

          {/* Amenities */}
          {hall.amenities && hall.amenities.length > 0 && (
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography 
                    variant="h6" 
                    className="font-semibold mb-3"
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    المرافق والخدمات
                  </Typography>
                  <List dense>
                    {hall.amenities.map((amenity, index) => (
                      <ListItem key={index} className="px-0">
                        <ListItemIcon className="min-w-0 mr-2">
                          <CheckIcon color="success" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography 
                              variant="body2"
                              sx={{ fontFamily: 'Cairo, sans-serif' }}
                            >
                              {amenity}
                            </Typography>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          )}

          {/* Rules */}
          {hall.rules && hall.rules.length > 0 && (
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography 
                    variant="h6" 
                    className="font-semibold mb-3"
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    قوانين وشروط الاستخدام
                  </Typography>
                  <List dense>
                    {hall.rules.map((rule, index) => (
                      <ListItem key={index} className="px-0">
                        <ListItemIcon className="min-w-0 mr-2">
                          <RuleIcon color="warning" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography 
                              variant="body2"
                              sx={{ fontFamily: 'Cairo, sans-serif' }}
                            >
                              {rule}
                            </Typography>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          )}

          {/* Statistics */}
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardContent>
                <Typography 
                  variant="h6" 
                  className="font-semibold mb-3"
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  إحصائيات القاعة
                </Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} md={3}>
                    <Box className="text-center p-3 bg-blue-50 rounded-lg">
                      <Typography 
                        variant="h4" 
                        className="font-bold text-blue-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        {Math.floor(Math.random() * 50) + 10}
                      </Typography>
                      <Typography 
                        variant="caption" 
                        className="text-blue-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        إجمالي الحجوزات
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} md={3}>
                    <Box className="text-center p-3 bg-green-50 rounded-lg">
                      <Typography 
                        variant="h4" 
                        className="font-bold text-green-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        {Math.floor(Math.random() * 30) + 60}%
                      </Typography>
                      <Typography 
                        variant="caption" 
                        className="text-green-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        معدل الإشغال
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} md={3}>
                    <Box className="text-center p-3 bg-yellow-50 rounded-lg">
                      <Typography 
                        variant="h4" 
                        className="font-bold text-yellow-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        {(Math.random() * 2 + 3).toFixed(1)}
                      </Typography>
                      <Typography 
                        variant="caption" 
                        className="text-yellow-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        التقييم
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} md={3}>
                    <Box className="text-center p-3 bg-purple-50 rounded-lg">
                      <Typography 
                        variant="h4" 
                        className="font-bold text-purple-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        {Math.floor(Math.random() * 20000) + 5000}
                      </Typography>
                      <Typography 
                        variant="caption" 
                        className="text-purple-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        الإيرادات (ر.س)
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions className="p-4">
        <Button 
          onClick={onClose}
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          إغلاق
        </Button>
        {canEdit && (
          <Button
            variant="contained"
            onClick={onEdit}
            startIcon={<EditIcon />}
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            تعديل
          </Button>
        )}
      </DialogActions>
    </>
  )
}

export default HallDetails
