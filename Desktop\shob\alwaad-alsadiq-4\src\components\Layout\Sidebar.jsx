import React from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Divider,
  Avatar
} from '@mui/material'
import {
  Dashboard as DashboardIcon,
  MeetingRoom as HallsIcon,
  EventNote as BookingsIcon,
  People as UsersIcon,
  Assessment as ReportsIcon,
  Person as ProfileIcon,
  Business as BusinessIcon
} from '@mui/icons-material'

const Sidebar = ({ open, onClose, isMobile }) => {
  const location = useLocation()
  const navigate = useNavigate()
  const { user } = useSelector((state) => state.auth)

  const menuItems = [
    {
      text: 'لوحة التحكم',
      icon: <DashboardIcon />,
      path: '/dashboard',
      roles: ['admin', 'manager', 'receptionist', 'customer']
    },
    {
      text: 'القاعات',
      icon: <HallsIcon />,
      path: '/halls',
      roles: ['admin', 'manager', 'receptionist', 'customer']
    },
    {
      text: 'الحجوزات',
      icon: <BookingsIcon />,
      path: '/bookings',
      roles: ['admin', 'manager', 'receptionist', 'customer']
    },
    {
      text: 'المستخدمين',
      icon: <UsersIcon />,
      path: '/users',
      roles: ['admin']
    },
    {
      text: 'التقارير',
      icon: <ReportsIcon />,
      path: '/reports',
      roles: ['admin', 'manager']
    },
    {
      text: 'الملف الشخصي',
      icon: <ProfileIcon />,
      path: '/profile',
      roles: ['admin', 'manager', 'receptionist', 'customer']
    }
  ]

  const filteredMenuItems = menuItems.filter(item => 
    item.roles.includes(user?.role)
  )

  const handleNavigation = (path) => {
    navigate(path)
    if (isMobile) {
      onClose()
    }
  }

  const sidebarContent = (
    <Box className="h-full flex flex-col bg-white">
      {/* Logo and Brand */}
      <Box className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3 space-x-reverse">
          <Avatar 
            sx={{ 
              bgcolor: '#0ea5e9', 
              width: 40, 
              height: 40 
            }}
          >
            <BusinessIcon />
          </Avatar>
          <div>
            <Typography 
              variant="h6" 
              className="font-bold text-gray-800"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              الوعد الصادق 4
            </Typography>
            <Typography 
              variant="caption" 
              className="text-gray-500"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              نظام إدارة القاعات
            </Typography>
          </div>
        </div>
      </Box>

      {/* User Info */}
      {user && (
        <Box className="p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3 space-x-reverse">
            <Avatar 
              sx={{ 
                bgcolor: '#d946ef', 
                width: 32, 
                height: 32,
                fontSize: '14px'
              }}
            >
              {user.name?.charAt(0)}
            </Avatar>
            <div className="flex-1 min-w-0">
              <Typography 
                variant="body2" 
                className="font-medium text-gray-800 truncate"
                sx={{ fontFamily: 'Cairo, sans-serif' }}
              >
                {user.name}
              </Typography>
              <Typography 
                variant="caption" 
                className="text-gray-500"
                sx={{ fontFamily: 'Cairo, sans-serif' }}
              >
                {user.role === 'admin' ? 'مدير' : 
                 user.role === 'manager' ? 'مدير فرع' :
                 user.role === 'receptionist' ? 'موظف استقبال' : 'عميل'}
              </Typography>
            </div>
          </div>
        </Box>
      )}

      {/* Navigation Menu */}
      <Box className="flex-1 overflow-y-auto">
        <List className="p-2">
          {filteredMenuItems.map((item) => {
            const isActive = location.pathname === item.path
            
            return (
              <ListItem key={item.text} disablePadding className="mb-1">
                <ListItemButton
                  onClick={() => handleNavigation(item.path)}
                  className={`rounded-lg transition-all duration-200 ${
                    isActive 
                      ? 'bg-primary-100 text-primary-700 shadow-sm' 
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  sx={{
                    borderRadius: '8px',
                    '&:hover': {
                      backgroundColor: isActive ? '#e0f2fe' : '#f5f5f5'
                    }
                  }}
                >
                  <ListItemIcon 
                    className={`min-w-0 mr-3 ${
                      isActive ? 'text-primary-700' : 'text-gray-500'
                    }`}
                  >
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText 
                    primary={item.text}
                    primaryTypographyProps={{
                      fontFamily: 'Cairo, sans-serif',
                      fontWeight: isActive ? 600 : 400,
                      fontSize: '14px'
                    }}
                  />
                </ListItemButton>
              </ListItem>
            )
          })}
        </List>
      </Box>

      {/* Footer */}
      <Box className="p-4 border-t border-gray-200">
        <Typography 
          variant="caption" 
          className="text-gray-400 text-center block"
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          © 2024 الوعد الصادق 4
        </Typography>
      </Box>
    </Box>
  )

  if (isMobile) {
    return (
      <Drawer
        anchor="right"
        open={open}
        onClose={onClose}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile
        }}
        sx={{
          '& .MuiDrawer-paper': {
            width: 280,
            boxSizing: 'border-box',
          },
        }}
      >
        {sidebarContent}
      </Drawer>
    )
  }

  return (
    <Box
      className={`transition-all duration-300 ${
        open ? 'w-72' : 'w-0'
      } overflow-hidden`}
      sx={{
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: 280,
          boxSizing: 'border-box',
        },
      }}
    >
      <Drawer
        variant="persistent"
        anchor="right"
        open={open}
        sx={{
          '& .MuiDrawer-paper': {
            width: 280,
            boxSizing: 'border-box',
            position: 'relative',
            height: '100%',
          },
        }}
      >
        {sidebarContent}
      </Drawer>
    </Box>
  )
}

export default Sidebar
