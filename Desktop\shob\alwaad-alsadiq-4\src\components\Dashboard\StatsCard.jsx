import React from 'react'
import {
  Card,
  CardContent,
  Typography,
  Avatar,
  Box,
  Chip
} from '@mui/material'
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon
} from '@mui/icons-material'

const StatsCard = ({ 
  title, 
  value, 
  icon, 
  color, 
  change, 
  changeType = 'neutral' 
}) => {
  const getChangeIcon = () => {
    switch (changeType) {
      case 'increase':
        return <TrendingUpIcon fontSize="small" />
      case 'decrease':
        return <TrendingDownIcon fontSize="small" />
      default:
        return <TrendingFlatIcon fontSize="small" />
    }
  }

  const getChangeColor = () => {
    switch (changeType) {
      case 'increase':
        return 'success'
      case 'decrease':
        return 'error'
      default:
        return 'default'
    }
  }

  return (
    <Card 
      className="h-full hover:shadow-lg transition-shadow duration-300"
      sx={{ 
        borderRadius: '12px',
        border: '1px solid #f1f5f9'
      }}
    >
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <Avatar
            sx={{
              bgcolor: color,
              width: 48,
              height: 48
            }}
          >
            {icon}
          </Avatar>
          
          {change && (
            <Chip
              icon={getChangeIcon()}
              label={change}
              color={getChangeColor()}
              size="small"
              variant="outlined"
              sx={{
                fontFamily: 'Cairo, sans-serif',
                fontSize: '12px'
              }}
            />
          )}
        </div>

        <Typography
          variant="h4"
          className="font-bold text-gray-800 mb-2"
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          {value}
        </Typography>

        <Typography
          variant="body2"
          className="text-gray-600"
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          {title}
        </Typography>

        {/* Progress indicator */}
        <Box className="mt-4">
          <div 
            className="h-1 bg-gray-200 rounded-full overflow-hidden"
          >
            <div
              className="h-full transition-all duration-500 ease-out"
              style={{
                backgroundColor: color,
                width: changeType === 'increase' ? '75%' : 
                       changeType === 'decrease' ? '25%' : '50%'
              }}
            />
          </div>
        </Box>
      </CardContent>
    </Card>
  )
}

export default StatsCard
