import React from 'react'
import {
  Box,
  Typography,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Chip
} from '@mui/material'
import {
  MeetingRoom as HallIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon
} from '@mui/icons-material'

const HallsOccupancy = ({ halls = [] }) => {
  // Sample occupancy data - in real app, this would be calculated from bookings
  const hallsWithOccupancy = halls.map((hall, index) => ({
    ...hall,
    occupancyRate: Math.floor(Math.random() * 100), // Random for demo
    trend: Math.random() > 0.5 ? 'up' : 'down',
    trendValue: Math.floor(Math.random() * 20)
  }))

  const getOccupancyColor = (rate) => {
    if (rate >= 80) return '#ef4444' // Red - High occupancy
    if (rate >= 60) return '#f59e0b' // Orange - Medium occupancy
    if (rate >= 40) return '#10b981' // Green - Good occupancy
    return '#6b7280' // Gray - Low occupancy
  }

  const getOccupancyStatus = (rate) => {
    if (rate >= 80) return 'مشغولة'
    if (rate >= 60) return 'متوسطة'
    if (rate >= 40) return 'متاحة'
    return 'فارغة'
  }

  if (!halls || halls.length === 0) {
    return (
      <Box className="text-center py-8">
        <Avatar
          sx={{
            bgcolor: '#f3f4f6',
            width: 64,
            height: 64,
            margin: '0 auto 16px'
          }}
        >
          <HallIcon sx={{ color: '#9ca3af' }} />
        </Avatar>
        <Typography
          variant="body2"
          className="text-gray-500"
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          لا توجد قاعات متاحة
        </Typography>
      </Box>
    )
  }

  return (
    <Box>
      <List className="space-y-3">
        {hallsWithOccupancy.slice(0, 6).map((hall) => (
          <ListItem 
            key={hall.id} 
            className="bg-gray-50 rounded-lg p-3"
            sx={{ 
              borderRadius: '8px',
              border: '1px solid #f1f5f9'
            }}
          >
            <ListItemIcon>
              <Avatar
                sx={{
                  bgcolor: getOccupancyColor(hall.occupancyRate),
                  width: 32,
                  height: 32
                }}
              >
                <HallIcon fontSize="small" />
              </Avatar>
            </ListItemIcon>
            
            <ListItemText
              primary={
                <div className="flex justify-between items-center mb-2">
                  <Typography
                    variant="body2"
                    className="font-medium"
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    {hall.name || `قاعة ${hall.id}`}
                  </Typography>
                  
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Chip
                      label={getOccupancyStatus(hall.occupancyRate)}
                      size="small"
                      sx={{
                        bgcolor: getOccupancyColor(hall.occupancyRate),
                        color: 'white',
                        fontFamily: 'Cairo, sans-serif',
                        fontSize: '10px'
                      }}
                    />
                    
                    <div className="flex items-center">
                      {hall.trend === 'up' ? (
                        <TrendingUpIcon 
                          fontSize="small" 
                          className="text-green-500 mr-1" 
                        />
                      ) : (
                        <TrendingDownIcon 
                          fontSize="small" 
                          className="text-red-500 mr-1" 
                        />
                      )}
                      <Typography
                        variant="caption"
                        className={hall.trend === 'up' ? 'text-green-500' : 'text-red-500'}
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        {hall.trendValue}%
                      </Typography>
                    </div>
                  </div>
                </div>
              }
              secondary={
                <Box>
                  <div className="flex justify-between items-center mb-1">
                    <Typography
                      variant="caption"
                      className="text-gray-600"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      معدل الإشغال
                    </Typography>
                    <Typography
                      variant="caption"
                      className="font-medium"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {hall.occupancyRate}%
                    </Typography>
                  </div>
                  
                  <LinearProgress
                    variant="determinate"
                    value={hall.occupancyRate}
                    sx={{
                      height: 6,
                      borderRadius: 3,
                      backgroundColor: '#e5e7eb',
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: getOccupancyColor(hall.occupancyRate),
                        borderRadius: 3
                      }
                    }}
                  />
                  
                  <Typography
                    variant="caption"
                    className="text-gray-500 mt-1 block"
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    {hall.type || 'قاعة عامة'} • السعة: {hall.capacity || 50} شخص
                  </Typography>
                </Box>
              }
            />
          </ListItem>
        ))}
      </List>
      
      {halls.length > 6 && (
        <Box className="text-center mt-4">
          <Typography
            variant="caption"
            className="text-gray-500"
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            +{halls.length - 6} قاعات أخرى
          </Typography>
        </Box>
      )}
    </Box>
  )
}

export default HallsOccupancy
