import React from 'react'
import { Navigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { Alert, Box } from '@mui/material'

const ProtectedRoute = ({ children, requiredRole = null }) => {
  const { isAuthenticated, user } = useSelector((state) => state.auth)

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }

  // Check role-based access
  if (requiredRole) {
    const userRole = user?.role
    
    // If requiredRole is an array, check if user role is in the array
    if (Array.isArray(requiredRole)) {
      if (!requiredRole.includes(userRole)) {
        return (
          <Box className="flex items-center justify-center min-h-screen">
            <Alert severity="error" className="max-w-md">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">غير مصرح لك بالوصول</h3>
                <p>ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة.</p>
              </div>
            </Alert>
          </Box>
        )
      }
    } else {
      // If requiredRole is a string, check exact match
      if (userRole !== requiredRole) {
        return (
          <Box className="flex items-center justify-center min-h-screen">
            <Alert severity="error" className="max-w-md">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">غير مصرح لك بالوصول</h3>
                <p>ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة.</p>
              </div>
            </Alert>
          </Box>
        )
      }
    }
  }

  return children
}

export default ProtectedRoute
