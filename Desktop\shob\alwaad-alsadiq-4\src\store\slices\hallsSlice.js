import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import axios from 'axios'

const API_URL = '/api/halls'

// Async thunks
export const fetchHalls = createAsyncThunk(
  'halls/fetchHalls',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(API_URL)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response.data.message)
    }
  }
)

export const createHall = createAsyncThunk(
  'halls/createHall',
  async (hallData, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.post(API_URL, hallData, {
        headers: { Authorization: `Bearer ${token}` }
      })
      return response.data
    } catch (error) {
      return rejectWithValue(error.response.data.message)
    }
  }
)

export const updateHall = createAsyncThunk(
  'halls/updateHall',
  async ({ id, hallData }, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.put(`${API_URL}/${id}`, hallData, {
        headers: { Authorization: `Bearer ${token}` }
      })
      return response.data
    } catch (error) {
      return rejectWithValue(error.response.data.message)
    }
  }
)

export const deleteHall = createAsyncThunk(
  'halls/deleteHall',
  async (id, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token')
      await axios.delete(`${API_URL}/${id}`, {
        headers: { Authorization: `Bearer ${token}` }
      })
      return id
    } catch (error) {
      return rejectWithValue(error.response.data.message)
    }
  }
)

export const getHallAvailability = createAsyncThunk(
  'halls/getHallAvailability',
  async ({ hallId, date }, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_URL}/${hallId}/availability?date=${date}`)
      return { hallId, availability: response.data }
    } catch (error) {
      return rejectWithValue(error.response.data.message)
    }
  }
)

const initialState = {
  halls: [],
  selectedHall: null,
  availability: {},
  isLoading: false,
  error: null,
  filters: {
    type: 'all',
    capacity: 'all',
    status: 'all',
  }
}

const hallsSlice = createSlice({
  name: 'halls',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setSelectedHall: (state, action) => {
      state.selectedHall = action.payload
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload }
    },
    clearFilters: (state) => {
      state.filters = {
        type: 'all',
        capacity: 'all',
        status: 'all',
      }
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch halls
      .addCase(fetchHalls.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchHalls.fulfilled, (state, action) => {
        state.isLoading = false
        state.halls = action.payload
        state.error = null
      })
      .addCase(fetchHalls.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
      
      // Create hall
      .addCase(createHall.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(createHall.fulfilled, (state, action) => {
        state.isLoading = false
        state.halls.push(action.payload)
        state.error = null
      })
      .addCase(createHall.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
      
      // Update hall
      .addCase(updateHall.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(updateHall.fulfilled, (state, action) => {
        state.isLoading = false
        const index = state.halls.findIndex(hall => hall.id === action.payload.id)
        if (index !== -1) {
          state.halls[index] = action.payload
        }
        state.error = null
      })
      .addCase(updateHall.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
      
      // Delete hall
      .addCase(deleteHall.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(deleteHall.fulfilled, (state, action) => {
        state.isLoading = false
        state.halls = state.halls.filter(hall => hall.id !== action.payload)
        state.error = null
      })
      .addCase(deleteHall.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
      
      // Get hall availability
      .addCase(getHallAvailability.fulfilled, (state, action) => {
        state.availability[action.payload.hallId] = action.payload.availability
      })
  },
})

export const { clearError, setSelectedHall, setFilters, clearFilters } = hallsSlice.actions
export default hallsSlice.reducer
