import React from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Box,
  Typography,
  Button,
  Avatar
} from '@mui/material'
import {
  Home as HomeIcon,
  ArrowBack as BackIcon,
  SearchOff as NotFoundIcon
} from '@mui/icons-material'

const NotFound = () => {
  const navigate = useNavigate()

  const handleGoHome = () => {
    navigate('/dashboard')
  }

  const handleGoBack = () => {
    navigate(-1)
  }

  return (
    <Box 
      className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100"
      dir="rtl"
    >
      <div className="text-center max-w-md mx-auto p-8">
        {/* 404 Icon */}
        <Avatar
          sx={{
            bgcolor: '#ef4444',
            width: 120,
            height: 120,
            margin: '0 auto 32px',
            fontSize: '48px'
          }}
        >
          <NotFoundIcon sx={{ fontSize: 60 }} />
        </Avatar>

        {/* 404 Text */}
        <Typography
          variant="h1"
          className="font-bold text-gray-800 mb-4"
          sx={{ 
            fontFamily: 'Cairo, sans-serif',
            fontSize: '6rem',
            lineHeight: 1
          }}
        >
          404
        </Typography>

        <Typography
          variant="h4"
          className="font-semibold text-gray-700 mb-4"
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          الصفحة غير موجودة
        </Typography>

        <Typography
          variant="body1"
          className="text-gray-600 mb-8"
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.
        </Typography>

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button
            variant="contained"
            size="large"
            startIcon={<HomeIcon />}
            onClick={handleGoHome}
            fullWidth
            sx={{
              fontFamily: 'Cairo, sans-serif',
              fontSize: '16px',
              fontWeight: 600,
              borderRadius: '8px',
              textTransform: 'none',
              py: 1.5
            }}
          >
            العودة للصفحة الرئيسية
          </Button>

          <Button
            variant="outlined"
            size="large"
            startIcon={<BackIcon />}
            onClick={handleGoBack}
            fullWidth
            sx={{
              fontFamily: 'Cairo, sans-serif',
              fontSize: '16px',
              fontWeight: 600,
              borderRadius: '8px',
              textTransform: 'none',
              py: 1.5
            }}
          >
            العودة للصفحة السابقة
          </Button>
        </div>

        {/* Help Text */}
        <Typography
          variant="caption"
          className="text-gray-500 mt-8 block"
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع الدعم الفني
        </Typography>
      </div>
    </Box>
  )
}

export default NotFound
