import React from 'react'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Avatar
} from '@mui/material'
import {
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  CheckCircle as SuccessIcon
} from '@mui/icons-material'

const ConfirmDialog = ({
  open,
  title,
  message,
  onConfirm,
  onCancel,
  confirmText = 'تأكيد',
  cancelText = 'إلغاء',
  severity = 'warning',
  loading = false
}) => {
  const getIcon = () => {
    switch (severity) {
      case 'error':
        return <ErrorIcon sx={{ fontSize: 48 }} />
      case 'info':
        return <InfoIcon sx={{ fontSize: 48 }} />
      case 'success':
        return <SuccessIcon sx={{ fontSize: 48 }} />
      default:
        return <WarningIcon sx={{ fontSize: 48 }} />
    }
  }

  const getColor = () => {
    switch (severity) {
      case 'error':
        return '#ef4444'
      case 'info':
        return '#3b82f6'
      case 'success':
        return '#10b981'
      default:
        return '#f59e0b'
    }
  }

  const getButtonColor = () => {
    switch (severity) {
      case 'error':
        return 'error'
      case 'info':
        return 'info'
      case 'success':
        return 'success'
      default:
        return 'warning'
    }
  }

  return (
    <Dialog
      open={open}
      onClose={onCancel}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '12px'
        }
      }}
    >
      <DialogContent className="text-center pt-8 pb-4">
        <Avatar
          sx={{
            bgcolor: getColor(),
            width: 80,
            height: 80,
            margin: '0 auto 24px',
            color: 'white'
          }}
        >
          {getIcon()}
        </Avatar>

        <Typography
          variant="h5"
          className="font-semibold mb-3"
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          {title}
        </Typography>

        <Typography
          variant="body1"
          className="text-gray-600 leading-relaxed"
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          {message}
        </Typography>
      </DialogContent>

      <DialogActions className="p-6 pt-0 justify-center space-x-3 space-x-reverse">
        <Button
          onClick={onCancel}
          variant="outlined"
          size="large"
          disabled={loading}
          sx={{
            fontFamily: 'Cairo, sans-serif',
            minWidth: 120,
            borderRadius: '8px'
          }}
        >
          {cancelText}
        </Button>
        
        <Button
          onClick={onConfirm}
          variant="contained"
          color={getButtonColor()}
          size="large"
          disabled={loading}
          sx={{
            fontFamily: 'Cairo, sans-serif',
            minWidth: 120,
            borderRadius: '8px'
          }}
        >
          {loading ? 'جاري التنفيذ...' : confirmText}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default ConfirmDialog
