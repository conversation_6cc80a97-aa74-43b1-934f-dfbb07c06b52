import React, { useState, useEffect } from 'react'
import { useDispatch } from 'react-redux'
import {
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  MenuItem,
  FormControlLabel,
  Switch,
  Typography,
  Box,
  Alert,
  IconButton,
  InputAdornment
} from '@mui/material'
import {
  Close as CloseIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Lock as LockIcon,
  Visibility,
  VisibilityOff
} from '@mui/icons-material'

import { createUser, updateUser } from '../../store/slices/usersSlice'

const UserForm = ({ user, onClose, onSuccess }) => {
  const dispatch = useDispatch()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    role: 'customer',
    status: 'active'
  })

  const [formErrors, setFormErrors] = useState({})

  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        phone: user.phone || '',
        password: '',
        confirmPassword: '',
        role: user.role || 'customer',
        status: user.status || 'active'
      })
    }
  }, [user])

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
    
    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = () => {
    const errors = {}

    if (!formData.name.trim()) {
      errors.name = 'الاسم مطلوب'
    } else if (formData.name.length < 2) {
      errors.name = 'الاسم يجب أن يكون حرفين على الأقل'
    }

    if (!formData.email.trim()) {
      errors.email = 'البريد الإلكتروني مطلوب'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'البريد الإلكتروني غير صحيح'
    }

    if (formData.phone && !/^[0-9+\-\s()]+$/.test(formData.phone)) {
      errors.phone = 'رقم الهاتف غير صحيح'
    }

    // Password validation only for new users or when password is provided
    if (!user || formData.password) {
      if (!formData.password) {
        errors.password = 'كلمة المرور مطلوبة'
      } else if (formData.password.length < 6) {
        errors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
      }

      if (!formData.confirmPassword) {
        errors.confirmPassword = 'تأكيد كلمة المرور مطلوب'
      } else if (formData.password !== formData.confirmPassword) {
        errors.confirmPassword = 'كلمة المرور غير متطابقة'
      }
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      const userData = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        role: formData.role,
        status: formData.status
      }

      // Only include password if it's provided
      if (formData.password) {
        userData.password = formData.password
      }

      if (user) {
        await dispatch(updateUser({ id: user.id, userData })).unwrap()
      } else {
        await dispatch(createUser(userData)).unwrap()
      }
      
      onSuccess()
    } catch (error) {
      setError(error.message || 'حدث خطأ أثناء حفظ البيانات')
    } finally {
      setLoading(false)
    }
  }

  const handleTogglePassword = () => {
    setShowPassword(!showPassword)
  }

  const userRoles = [
    { value: 'admin', label: 'مدير النظام' },
    { value: 'manager', label: 'مدير فرع' },
    { value: 'receptionist', label: 'موظف استقبال' },
    { value: 'customer', label: 'عميل' }
  ]

  return (
    <form onSubmit={handleSubmit}>
      <DialogTitle className="flex justify-between items-center">
        <Typography 
          variant="h6" 
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          {user ? 'تعديل المستخدم' : 'إضافة مستخدم جديد'}
        </Typography>
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers className="space-y-4">
        {error && (
          <Alert severity="error" sx={{ fontFamily: 'Cairo, sans-serif' }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <Typography 
              variant="subtitle1" 
              className="font-semibold mb-3"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              المعلومات الأساسية
            </Typography>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              required
              name="name"
              label="الاسم الكامل"
              value={formData.name}
              onChange={handleChange}
              error={!!formErrors.name}
              helperText={formErrors.name}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PersonIcon className="text-gray-400" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                },
                '& .MuiHelperText-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              required
              name="email"
              type="email"
              label="البريد الإلكتروني"
              value={formData.email}
              onChange={handleChange}
              error={!!formErrors.email}
              helperText={formErrors.email}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <EmailIcon className="text-gray-400" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                },
                '& .MuiHelperText-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              name="phone"
              label="رقم الهاتف"
              value={formData.phone}
              onChange={handleChange}
              error={!!formErrors.phone}
              helperText={formErrors.phone}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PhoneIcon className="text-gray-400" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                },
                '& .MuiHelperText-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              select
              name="role"
              label="الدور"
              value={formData.role}
              onChange={handleChange}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            >
              {userRoles.map((role) => (
                <MenuItem key={role.value} value={role.value}>
                  {role.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>

          {/* Password Section */}
          <Grid item xs={12}>
            <Typography 
              variant="subtitle1" 
              className="font-semibold mb-3"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              {user ? 'تغيير كلمة المرور (اختياري)' : 'كلمة المرور'}
            </Typography>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              required={!user}
              name="password"
              type={showPassword ? 'text' : 'password'}
              label={user ? 'كلمة المرور الجديدة' : 'كلمة المرور'}
              value={formData.password}
              onChange={handleChange}
              error={!!formErrors.password}
              helperText={formErrors.password}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LockIcon className="text-gray-400" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={handleTogglePassword}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                },
                '& .MuiHelperText-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              required={!user || formData.password}
              name="confirmPassword"
              type={showPassword ? 'text' : 'password'}
              label="تأكيد كلمة المرور"
              value={formData.confirmPassword}
              onChange={handleChange}
              error={!!formErrors.confirmPassword}
              helperText={formErrors.confirmPassword}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LockIcon className="text-gray-400" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                },
                '& .MuiHelperText-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />
          </Grid>

          {/* Status */}
          <Grid item xs={12}>
            <Typography 
              variant="subtitle1" 
              className="font-semibold mb-3"
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              الحالة
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.status === 'active'}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    status: e.target.checked ? 'active' : 'inactive'
                  }))}
                />
              }
              label={
                <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
                  المستخدم نشط
                </Typography>
              }
            />
          </Grid>

          {/* Additional Information */}
          {user && (
            <Grid item xs={12}>
              <Box className="bg-gray-50 p-4 rounded-lg">
                <Typography 
                  variant="subtitle2" 
                  className="font-semibold mb-2"
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  معلومات إضافية
                </Typography>
                
                <Typography 
                  variant="caption" 
                  className="text-gray-600 block"
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  تاريخ التسجيل: {new Date(user.created_at).toLocaleDateString('ar-SA')}
                </Typography>
                
                <Typography 
                  variant="caption" 
                  className="text-gray-600 block"
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  آخر تحديث: {new Date(user.updated_at).toLocaleDateString('ar-SA')}
                </Typography>
              </Box>
            </Grid>
          )}
        </Grid>
      </DialogContent>

      <DialogActions className="p-4">
        <Button 
          onClick={onClose}
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          إلغاء
        </Button>
        <Button
          type="submit"
          variant="contained"
          disabled={loading}
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          {loading ? 'جاري الحفظ...' : (user ? 'تحديث' : 'إضافة')}
        </Button>
      </DialogActions>
    </form>
  )
}

export default UserForm
