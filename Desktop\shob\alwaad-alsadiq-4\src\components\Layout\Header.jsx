import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import {
  AppBar,
  Toolbar,
  IconButton,
  Typography,
  Badge,
  Menu,
  MenuItem,
  Avatar,
  Box,
  Divider,
  ListItemIcon,
  ListItemText
} from '@mui/material'
import {
  Menu as MenuIcon,
  Notifications as NotificationsIcon,
  AccountCircle as AccountIcon,
  Logout as LogoutIcon,
  Settings as SettingsIcon,
  Person as PersonIcon
} from '@mui/icons-material'

import { logoutUser } from '../../store/slices/authSlice'
import { markAsRead, markAllAsRead } from '../../store/slices/notificationsSlice'
import NotificationsList from '../Notifications/NotificationsList'

const Header = ({ onSidebarToggle, sidebarOpen }) => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { user } = useSelector((state) => state.auth)
  const { notifications, unreadCount } = useSelector((state) => state.notifications)

  const [anchorEl, setAnchorEl] = useState(null)
  const [notificationsAnchorEl, setNotificationsAnchorEl] = useState(null)

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget)
  }

  const handleProfileMenuClose = () => {
    setAnchorEl(null)
  }

  const handleNotificationsOpen = (event) => {
    setNotificationsAnchorEl(event.currentTarget)
  }

  const handleNotificationsClose = () => {
    setNotificationsAnchorEl(null)
  }

  const handleLogout = () => {
    dispatch(logoutUser())
    handleProfileMenuClose()
    navigate('/login')
  }

  const handleProfile = () => {
    navigate('/profile')
    handleProfileMenuClose()
  }

  const handleMarkAllAsRead = () => {
    dispatch(markAllAsRead())
  }

  const handleNotificationClick = (notification) => {
    if (!notification.read) {
      dispatch(markAsRead(notification.id))
    }
    // Navigate to relevant page based on notification type
    if (notification.type === 'booking') {
      navigate('/bookings')
    } else if (notification.type === 'payment') {
      navigate('/reports')
    }
    handleNotificationsClose()
  }

  return (
    <AppBar 
      position="static" 
      elevation={1}
      sx={{ 
        backgroundColor: 'white',
        color: '#374151',
        borderBottom: '1px solid #e5e7eb'
      }}
    >
      <Toolbar className="justify-between">
        {/* Left side - Menu button and title */}
        <Box className="flex items-center">
          <IconButton
            edge="start"
            color="inherit"
            aria-label="toggle sidebar"
            onClick={onSidebarToggle}
            className="mr-2"
          >
            <MenuIcon />
          </IconButton>
          
          <Typography 
            variant="h6" 
            component="div"
            className="font-semibold text-gray-800"
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            مرحباً، {user?.name}
          </Typography>
        </Box>

        {/* Right side - Notifications and profile */}
        <Box className="flex items-center space-x-2 space-x-reverse">
          {/* Notifications */}
          <IconButton
            color="inherit"
            onClick={handleNotificationsOpen}
            aria-label={`${unreadCount} إشعارات جديدة`}
          >
            <Badge badgeContent={unreadCount} color="error">
              <NotificationsIcon />
            </Badge>
          </IconButton>

          {/* Profile */}
          <IconButton
            edge="end"
            aria-label="account of current user"
            aria-controls="profile-menu"
            aria-haspopup="true"
            onClick={handleProfileMenuOpen}
            color="inherit"
          >
            <Avatar 
              sx={{ 
                bgcolor: '#0ea5e9', 
                width: 32, 
                height: 32,
                fontSize: '14px'
              }}
            >
              {user?.name?.charAt(0)}
            </Avatar>
          </IconButton>
        </Box>
      </Toolbar>

      {/* Profile Menu */}
      <Menu
        id="profile-menu"
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleProfileMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        PaperProps={{
          sx: {
            mt: 1,
            minWidth: 200,
            borderRadius: '8px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }
        }}
      >
        <Box className="px-4 py-3 border-b border-gray-200">
          <Typography 
            variant="body2" 
            className="font-medium text-gray-800"
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            {user?.name}
          </Typography>
          <Typography 
            variant="caption" 
            className="text-gray-500"
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            {user?.email}
          </Typography>
        </Box>
        
        <MenuItem onClick={handleProfile}>
          <ListItemIcon>
            <PersonIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText 
            primary="الملف الشخصي"
            primaryTypographyProps={{
              fontFamily: 'Cairo, sans-serif'
            }}
          />
        </MenuItem>
        
        <Divider />
        
        <MenuItem onClick={handleLogout}>
          <ListItemIcon>
            <LogoutIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText 
            primary="تسجيل الخروج"
            primaryTypographyProps={{
              fontFamily: 'Cairo, sans-serif'
            }}
          />
        </MenuItem>
      </Menu>

      {/* Notifications Menu */}
      <Menu
        id="notifications-menu"
        anchorEl={notificationsAnchorEl}
        open={Boolean(notificationsAnchorEl)}
        onClose={handleNotificationsClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        PaperProps={{
          sx: {
            mt: 1,
            width: 350,
            maxHeight: 400,
            borderRadius: '8px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }
        }}
      >
        <NotificationsList
          notifications={notifications}
          onNotificationClick={handleNotificationClick}
          onMarkAllAsRead={handleMarkAllAsRead}
          onClose={handleNotificationsClose}
        />
      </Menu>
    </AppBar>
  )
}

export default Header
