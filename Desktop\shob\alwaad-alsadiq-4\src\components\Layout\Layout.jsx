import React, { useState } from 'react'
import { Outlet } from 'react-router-dom'
import { Box, useMediaQuery, useTheme } from '@mui/material'

import Sidebar from './Sidebar'
import Header from './Header'

const Layout = () => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile)

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen)
  }

  return (
    <Box className="flex h-screen bg-gray-50" dir="rtl">
      {/* Sidebar */}
      <Sidebar 
        open={sidebarOpen} 
        onClose={() => setSidebarOpen(false)}
        isMobile={isMobile}
      />

      {/* Main Content */}
      <Box className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header 
          onSidebarToggle={handleSidebarToggle}
          sidebarOpen={sidebarOpen}
        />

        {/* Page Content */}
        <Box 
          component="main" 
          className="flex-1 overflow-auto p-4 md:p-6"
          sx={{
            backgroundColor: '#f8fafc',
            minHeight: 'calc(100vh - 64px)'
          }}
        >
          <div className="max-w-7xl mx-auto">
            <Outlet />
          </div>
        </Box>
      </Box>

      {/* Mobile Overlay */}
      {isMobile && sidebarOpen && (
        <Box
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </Box>
  )
}

export default Layout
