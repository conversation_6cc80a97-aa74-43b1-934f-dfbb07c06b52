import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Dialog,
  Fab,
  TextField,
  MenuItem,
  InputAdornment,
  Avatar,
  Menu
} from '@mui/material'
import {
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  MoreVert as MoreIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  EventNote as BookingIcon,
  Cancel as CancelIcon,
  CheckCircle as ConfirmIcon
} from '@mui/icons-material'

import { 
  fetchBookings, 
  cancelBooking, 
  confirmBooking 
} from '../../store/slices/bookingsSlice'
import { fetchHalls } from '../../store/slices/hallsSlice'
import BookingForm from '../../components/Bookings/BookingForm'
import BookingDetails from '../../components/Bookings/BookingDetails'
import ConfirmDialog from '../../components/Common/ConfirmDialog'
import LoadingSpinner from '../../components/LoadingSpinner/LoadingSpinner'

const Bookings = () => {
  const dispatch = useDispatch()
  const { bookings, isLoading, pagination } = useSelector((state) => state.bookings)
  const { halls } = useSelector((state) => state.halls)
  const { user } = useSelector((state) => state.auth)

  const [selectedBooking, setSelectedBooking] = useState(null)
  const [showForm, setShowForm] = useState(false)
  const [showDetails, setShowDetails] = useState(false)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [actionType, setActionType] = useState('')
  const [bookingToAction, setBookingToAction] = useState(null)
  const [anchorEl, setAnchorEl] = useState(null)
  const [menuBooking, setMenuBooking] = useState(null)

  // Filters
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [hallFilter, setHallFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('')

  const canManageBookings = ['admin', 'manager', 'receptionist'].includes(user?.role)
  const canCreateBookings = ['admin', 'manager', 'receptionist', 'customer'].includes(user?.role)

  useEffect(() => {
    dispatch(fetchBookings({
      page: pagination.page,
      limit: pagination.limit,
      search: searchTerm,
      status: statusFilter !== 'all' ? statusFilter : undefined,
      hall: hallFilter !== 'all' ? hallFilter : undefined,
      date: dateFilter || undefined
    }))
    dispatch(fetchHalls())
  }, [dispatch, pagination.page, pagination.limit, searchTerm, statusFilter, hallFilter, dateFilter])

  const handleAddBooking = () => {
    setSelectedBooking(null)
    setShowForm(true)
  }

  const handleEditBooking = (booking) => {
    setSelectedBooking(booking)
    setShowForm(true)
    handleCloseMenu()
  }

  const handleViewBooking = (booking) => {
    setSelectedBooking(booking)
    setShowDetails(true)
    handleCloseMenu()
  }

  const handleMenuClick = (event, booking) => {
    setAnchorEl(event.currentTarget)
    setMenuBooking(booking)
  }

  const handleCloseMenu = () => {
    setAnchorEl(null)
    setMenuBooking(null)
  }

  const handleActionBooking = (booking, action) => {
    setBookingToAction(booking)
    setActionType(action)
    setShowConfirmDialog(true)
    handleCloseMenu()
  }

  const confirmAction = async () => {
    if (bookingToAction && actionType) {
      if (actionType === 'cancel') {
        await dispatch(cancelBooking(bookingToAction.id))
      } else if (actionType === 'confirm') {
        await dispatch(confirmBooking(bookingToAction.id))
      }
      setShowConfirmDialog(false)
      setBookingToAction(null)
      setActionType('')
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed':
        return 'success'
      case 'pending':
        return 'warning'
      case 'cancelled':
        return 'error'
      case 'completed':
        return 'info'
      default:
        return 'default'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'confirmed':
        return 'مؤكد'
      case 'pending':
        return 'في الانتظار'
      case 'cancelled':
        return 'ملغي'
      case 'completed':
        return 'مكتمل'
      default:
        return status
    }
  }

  const formatDate = (dateString) => {
    try {
      return new Date(dateString).toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    } catch (error) {
      return dateString
    }
  }

  const formatTime = (timeString) => {
    try {
      return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      })
    } catch (error) {
      return timeString
    }
  }

  const handlePageChange = (event, newPage) => {
    dispatch(setPagination({ page: newPage + 1 }))
  }

  const handleRowsPerPageChange = (event) => {
    dispatch(setPagination({ 
      page: 1, 
      limit: parseInt(event.target.value, 10) 
    }))
  }

  if (isLoading && bookings.length === 0) {
    return <LoadingSpinner message="جاري تحميل الحجوزات..." />
  }

  return (
    <Box className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <Typography 
            variant="h4" 
            className="font-bold text-gray-800"
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            إدارة الحجوزات
          </Typography>
          <Typography 
            variant="body2" 
            className="text-gray-600 mt-1"
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            عرض وإدارة جميع حجوزات القاعات
          </Typography>
        </div>
        
        {canCreateBookings && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddBooking}
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            حجز جديد
          </Button>
        )}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <TextField
              fullWidth
              placeholder="البحث في الحجوزات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon className="text-gray-400" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiInputBase-input': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />
            
            <TextField
              fullWidth
              select
              label="الحالة"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            >
              <MenuItem value="all">جميع الحالات</MenuItem>
              <MenuItem value="pending">في الانتظار</MenuItem>
              <MenuItem value="confirmed">مؤكد</MenuItem>
              <MenuItem value="completed">مكتمل</MenuItem>
              <MenuItem value="cancelled">ملغي</MenuItem>
            </TextField>
            
            <TextField
              fullWidth
              select
              label="القاعة"
              value={hallFilter}
              onChange={(e) => setHallFilter(e.target.value)}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            >
              <MenuItem value="all">جميع القاعات</MenuItem>
              {halls.map((hall) => (
                <MenuItem key={hall.id} value={hall.id}>
                  {hall.name}
                </MenuItem>
              ))}
            </TextField>
            
            <TextField
              fullWidth
              type="date"
              label="التاريخ"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              InputLabelProps={{ shrink: true }}
              sx={{
                '& .MuiInputLabel-root': {
                  fontFamily: 'Cairo, sans-serif'
                }
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Bookings Table */}
      <Card>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                  العميل
                </TableCell>
                <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                  القاعة
                </TableCell>
                <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                  التاريخ
                </TableCell>
                <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                  الوقت
                </TableCell>
                <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                  الحالة
                </TableCell>
                <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                  المبلغ
                </TableCell>
                <TableCell sx={{ fontFamily: 'Cairo, sans-serif', fontWeight: 600 }}>
                  الإجراءات
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {bookings.map((booking) => (
                <TableRow 
                  key={booking.id}
                  hover
                  className="cursor-pointer"
                  onClick={() => handleViewBooking(booking)}
                >
                  <TableCell>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <Avatar
                        sx={{
                          bgcolor: '#0ea5e9',
                          width: 32,
                          height: 32,
                          fontSize: '14px'
                        }}
                      >
                        {booking.customerName?.charAt(0) || 'ع'}
                      </Avatar>
                      <div>
                        <Typography
                          variant="body2"
                          className="font-medium"
                          sx={{ fontFamily: 'Cairo, sans-serif' }}
                        >
                          {booking.customerName || 'غير محدد'}
                        </Typography>
                        <Typography
                          variant="caption"
                          className="text-gray-500"
                          sx={{ fontFamily: 'Cairo, sans-serif' }}
                        >
                          {booking.customerPhone || ''}
                        </Typography>
                      </div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <Typography
                      variant="body2"
                      className="font-medium"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {booking.hallName || 'غير محدد'}
                    </Typography>
                  </TableCell>
                  
                  <TableCell>
                    <Typography
                      variant="body2"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {formatDate(booking.date)}
                    </Typography>
                  </TableCell>
                  
                  <TableCell>
                    <Typography
                      variant="body2"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {formatTime(booking.startTime)} - {formatTime(booking.endTime)}
                    </Typography>
                  </TableCell>
                  
                  <TableCell>
                    <Chip
                      label={getStatusText(booking.status)}
                      color={getStatusColor(booking.status)}
                      size="small"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    />
                  </TableCell>
                  
                  <TableCell>
                    <Typography
                      variant="body2"
                      className="font-medium"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {booking.totalAmount || 0} ر.س
                    </Typography>
                  </TableCell>
                  
                  <TableCell>
                    <IconButton
                      onClick={(e) => {
                        e.stopPropagation()
                        handleMenuClick(e, booking)
                      }}
                    >
                      <MoreIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        
        <TablePagination
          component="div"
          count={pagination.total}
          page={pagination.page - 1}
          onPageChange={handlePageChange}
          rowsPerPage={pagination.limit}
          onRowsPerPageChange={handleRowsPerPageChange}
          rowsPerPageOptions={[5, 10, 25, 50]}
          labelRowsPerPage="عدد الصفوف في الصفحة:"
          labelDisplayedRows={({ from, to, count }) => 
            `${from}-${to} من ${count !== -1 ? count : `أكثر من ${to}`}`
          }
          sx={{
            '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
              fontFamily: 'Cairo, sans-serif'
            }
          }}
        />
      </Card>

      {/* Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
      >
        <MenuItem onClick={() => handleViewBooking(menuBooking)}>
          <ViewIcon className="mr-2" fontSize="small" />
          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
            عرض التفاصيل
          </Typography>
        </MenuItem>
        
        {canManageBookings && menuBooking?.status === 'pending' && (
          <MenuItem onClick={() => handleActionBooking(menuBooking, 'confirm')}>
            <ConfirmIcon className="mr-2" fontSize="small" />
            <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
              تأكيد الحجز
            </Typography>
          </MenuItem>
        )}
        
        {canManageBookings && (
          <MenuItem onClick={() => handleEditBooking(menuBooking)}>
            <EditIcon className="mr-2" fontSize="small" />
            <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
              تعديل
            </Typography>
          </MenuItem>
        )}
        
        {canManageBookings && ['pending', 'confirmed'].includes(menuBooking?.status) && (
          <MenuItem onClick={() => handleActionBooking(menuBooking, 'cancel')}>
            <CancelIcon className="mr-2" fontSize="small" />
            <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
              إلغاء الحجز
            </Typography>
          </MenuItem>
        )}
      </Menu>

      {/* Floating Action Button for Mobile */}
      {canCreateBookings && (
        <Fab
          color="primary"
          aria-label="add booking"
          onClick={handleAddBooking}
          sx={{
            position: 'fixed',
            bottom: 16,
            left: 16,
            display: { xs: 'flex', md: 'none' }
          }}
        >
          <AddIcon />
        </Fab>
      )}

      {/* Booking Form Dialog */}
      <Dialog
        open={showForm}
        onClose={() => setShowForm(false)}
        maxWidth="md"
        fullWidth
      >
        <BookingForm
          booking={selectedBooking}
          onClose={() => setShowForm(false)}
          onSuccess={() => {
            setShowForm(false)
            dispatch(fetchBookings())
          }}
        />
      </Dialog>

      {/* Booking Details Dialog */}
      <Dialog
        open={showDetails}
        onClose={() => setShowDetails(false)}
        maxWidth="md"
        fullWidth
      >
        <BookingDetails
          booking={selectedBooking}
          onClose={() => setShowDetails(false)}
          onEdit={() => {
            setShowDetails(false)
            setShowForm(true)
          }}
        />
      </Dialog>

      {/* Confirm Action Dialog */}
      <ConfirmDialog
        open={showConfirmDialog}
        title={actionType === 'cancel' ? 'إلغاء الحجز' : 'تأكيد الحجز'}
        message={
          actionType === 'cancel' 
            ? `هل أنت متأكد من إلغاء حجز "${bookingToAction?.customerName}"؟`
            : `هل أنت متأكد من تأكيد حجز "${bookingToAction?.customerName}"؟`
        }
        onConfirm={confirmAction}
        onCancel={() => {
          setShowConfirmDialog(false)
          setBookingToAction(null)
          setActionType('')
        }}
        confirmText={actionType === 'cancel' ? 'إلغاء الحجز' : 'تأكيد الحجز'}
        cancelText="إلغاء"
        severity={actionType === 'cancel' ? 'error' : 'success'}
      />
    </Box>
  )
}

export default Bookings
