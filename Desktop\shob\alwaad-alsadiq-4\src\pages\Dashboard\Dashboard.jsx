import React, { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  LinearProgress,
  Chip
} from '@mui/material'
import {
  TrendingUp as TrendingUpIcon,
  EventNote as BookingsIcon,
  MeetingRoom as HallsIcon,
  People as CustomersIcon,
  AttachMoney as RevenueIcon,
  Today as TodayIcon
} from '@mui/icons-material'

import { fetchDashboardStats } from '../../store/slices/reportsSlice'
import { fetchBookings } from '../../store/slices/bookingsSlice'
import { fetchHalls } from '../../store/slices/hallsSlice'
import StatsCard from '../../components/Dashboard/StatsCard'
import RecentBookings from '../../components/Dashboard/RecentBookings'
import RevenueChart from '../../components/Dashboard/RevenueChart'
import HallsOccupancy from '../../components/Dashboard/HallsOccupancy'
import LoadingSpinner from '../../components/LoadingSpinner/LoadingSpinner'

const Dashboard = () => {
  const dispatch = useDispatch()
  const { user } = useSelector((state) => state.auth)
  const { dashboardStats, isLoading } = useSelector((state) => state.reports)
  const { bookings } = useSelector((state) => state.bookings)
  const { halls } = useSelector((state) => state.halls)

  useEffect(() => {
    dispatch(fetchDashboardStats())
    dispatch(fetchBookings({ limit: 5, status: 'all' }))
    dispatch(fetchHalls())
  }, [dispatch])

  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return 'صباح الخير'
    if (hour < 18) return 'مساء الخير'
    return 'مساء الخير'
  }

  const statsCards = [
    {
      title: 'إجمالي الحجوزات',
      value: dashboardStats.totalBookings || 0,
      icon: <BookingsIcon />,
      color: '#0ea5e9',
      change: '+12%',
      changeType: 'increase'
    },
    {
      title: 'الإيرادات الشهرية',
      value: `${dashboardStats.monthlyRevenue || 0} ر.س`,
      icon: <RevenueIcon />,
      color: '#10b981',
      change: '+8%',
      changeType: 'increase'
    },
    {
      title: 'القاعات المتاحة',
      value: dashboardStats.totalHalls || 0,
      icon: <HallsIcon />,
      color: '#f59e0b',
      change: '0%',
      changeType: 'neutral'
    },
    {
      title: 'العملاء',
      value: dashboardStats.totalCustomers || 0,
      icon: <CustomersIcon />,
      color: '#8b5cf6',
      change: '+5%',
      changeType: 'increase'
    },
    {
      title: 'حجوزات اليوم',
      value: dashboardStats.todayBookings || 0,
      icon: <TodayIcon />,
      color: '#ef4444',
      change: '+3',
      changeType: 'increase'
    },
    {
      title: 'معدل الإشغال',
      value: `${dashboardStats.occupancyRate || 0}%`,
      icon: <TrendingUpIcon />,
      color: '#06b6d4',
      change: '+2%',
      changeType: 'increase'
    }
  ]

  if (isLoading) {
    return <LoadingSpinner message="جاري تحميل لوحة التحكم..." />
  }

  return (
    <Box className="space-y-6">
      {/* Welcome Section */}
      <Card className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <Typography 
                variant="h4" 
                className="font-bold mb-2"
                sx={{ fontFamily: 'Cairo, sans-serif' }}
              >
                {getGreeting()}، {user?.name}
              </Typography>
              <Typography 
                variant="body1" 
                className="opacity-90"
                sx={{ fontFamily: 'Cairo, sans-serif' }}
              >
                مرحباً بك في نظام إدارة قاعات الوعد الصادق 4
              </Typography>
              <Typography 
                variant="body2" 
                className="opacity-75 mt-2"
                sx={{ fontFamily: 'Cairo, sans-serif' }}
              >
                {new Date().toLocaleDateString('ar-SA', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </Typography>
            </div>
            <Avatar
              sx={{
                width: 80,
                height: 80,
                bgcolor: 'rgba(255, 255, 255, 0.2)',
                fontSize: '2rem'
              }}
            >
              {user?.name?.charAt(0)}
            </Avatar>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <Grid container spacing={3}>
        {statsCards.map((stat, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <StatsCard {...stat} />
          </Grid>
        ))}
      </Grid>

      {/* Charts and Tables */}
      <Grid container spacing={3}>
        {/* Revenue Chart */}
        <Grid item xs={12} lg={8}>
          <Card className="h-full">
            <CardContent className="p-6">
              <Typography 
                variant="h6" 
                className="font-semibold mb-4"
                sx={{ fontFamily: 'Cairo, sans-serif' }}
              >
                الإيرادات الشهرية
              </Typography>
              <RevenueChart />
            </CardContent>
          </Card>
        </Grid>

        {/* Halls Occupancy */}
        <Grid item xs={12} lg={4}>
          <Card className="h-full">
            <CardContent className="p-6">
              <Typography 
                variant="h6" 
                className="font-semibold mb-4"
                sx={{ fontFamily: 'Cairo, sans-serif' }}
              >
                إشغال القاعات
              </Typography>
              <HallsOccupancy halls={halls} />
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Bookings */}
        <Grid item xs={12}>
          <Card>
            <CardContent className="p-6">
              <div className="flex justify-between items-center mb-4">
                <Typography 
                  variant="h6" 
                  className="font-semibold"
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  أحدث الحجوزات
                </Typography>
                <Chip 
                  label={`${bookings.length} حجز`}
                  color="primary"
                  size="small"
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                />
              </div>
              <RecentBookings bookings={bookings} />
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}

export default Dashboard
