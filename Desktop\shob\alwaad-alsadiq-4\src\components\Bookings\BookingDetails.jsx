import React from 'react'
import { useSelector } from 'react-redux'
import {
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Chip,
  Grid,
  Card,
  CardContent,
  Divider,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material'
import {
  Close as CloseIcon,
  Edit as EditIcon,
  Person as PersonIcon,
  MeetingRoom as HallIcon,
  Schedule as ScheduleIcon,
  Payment as PaymentIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Notes as NotesIcon,
  AttachMoney as MoneyIcon
} from '@mui/icons-material'

const BookingDetails = ({ booking, onClose, onEdit }) => {
  const { user } = useSelector((state) => state.auth)
  const canEdit = ['admin', 'manager', 'receptionist'].includes(user?.role)

  if (!booking) {
    return null
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed':
        return 'success'
      case 'pending':
        return 'warning'
      case 'cancelled':
        return 'error'
      case 'completed':
        return 'info'
      default:
        return 'default'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'confirmed':
        return 'مؤكد'
      case 'pending':
        return 'في الانتظار'
      case 'cancelled':
        return 'ملغي'
      case 'completed':
        return 'مكتمل'
      default:
        return status
    }
  }

  const getPaymentStatusColor = (status) => {
    switch (status) {
      case 'paid':
        return 'success'
      case 'pending':
        return 'warning'
      case 'partial':
        return 'info'
      case 'refunded':
        return 'error'
      default:
        return 'default'
    }
  }

  const getPaymentStatusText = (status) => {
    switch (status) {
      case 'paid':
        return 'مدفوع'
      case 'pending':
        return 'في الانتظار'
      case 'partial':
        return 'مدفوع جزئياً'
      case 'refunded':
        return 'مسترد'
      default:
        return status
    }
  }

  const getPaymentMethodText = (method) => {
    switch (method) {
      case 'cash':
        return 'نقداً'
      case 'card':
        return 'بطاقة ائتمان'
      case 'bank_transfer':
        return 'تحويل بنكي'
      case 'online':
        return 'دفع إلكتروني'
      default:
        return method
    }
  }

  const formatDate = (dateString) => {
    try {
      return new Date(dateString).toLocaleDateString('ar-SA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    } catch (error) {
      return dateString
    }
  }

  const formatTime = (timeString) => {
    try {
      return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      })
    } catch (error) {
      return timeString
    }
  }

  const formatDateTime = (dateTimeString) => {
    try {
      return new Date(dateTimeString).toLocaleString('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch (error) {
      return dateTimeString
    }
  }

  return (
    <>
      <DialogTitle className="flex justify-between items-center">
        <div className="flex items-center space-x-3 space-x-reverse">
          <ScheduleIcon color="primary" />
          <div>
            <Typography 
              variant="h6" 
              sx={{ fontFamily: 'Cairo, sans-serif' }}
            >
              تفاصيل الحجز #{booking.id}
            </Typography>
            <div className="flex items-center space-x-2 space-x-reverse mt-1">
              <Chip
                label={getStatusText(booking.status)}
                color={getStatusColor(booking.status)}
                size="small"
                sx={{ fontFamily: 'Cairo, sans-serif' }}
              />
              <Chip
                label={getPaymentStatusText(booking.payment_status)}
                color={getPaymentStatusColor(booking.payment_status)}
                size="small"
                sx={{ fontFamily: 'Cairo, sans-serif' }}
              />
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2 space-x-reverse">
          {canEdit && (
            <IconButton onClick={onEdit} color="primary">
              <EditIcon />
            </IconButton>
          )}
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </div>
      </DialogTitle>

      <DialogContent dividers>
        <Grid container spacing={3}>
          {/* Customer Information */}
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Box className="flex items-center space-x-2 space-x-reverse mb-3">
                  <PersonIcon color="primary" />
                  <Typography 
                    variant="h6" 
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    معلومات العميل
                  </Typography>
                </Box>
                
                <List dense>
                  <ListItem className="px-0">
                    <ListItemIcon className="min-w-0 mr-2">
                      <PersonIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
                          {booking.customer_name}
                        </Typography>
                      }
                      secondary={
                        <Typography 
                          variant="caption" 
                          sx={{ fontFamily: 'Cairo, sans-serif' }}
                        >
                          اسم العميل
                        </Typography>
                      }
                    />
                  </ListItem>
                  
                  {booking.customer_phone && (
                    <ListItem className="px-0">
                      <ListItemIcon className="min-w-0 mr-2">
                        <PhoneIcon fontSize="small" />
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
                            {booking.customer_phone}
                          </Typography>
                        }
                        secondary={
                          <Typography 
                            variant="caption" 
                            sx={{ fontFamily: 'Cairo, sans-serif' }}
                          >
                            رقم الهاتف
                          </Typography>
                        }
                      />
                    </ListItem>
                  )}
                  
                  {booking.customer_email && (
                    <ListItem className="px-0">
                      <ListItemIcon className="min-w-0 mr-2">
                        <EmailIcon fontSize="small" />
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography sx={{ fontFamily: 'Cairo, sans-serif' }}>
                            {booking.customer_email}
                          </Typography>
                        }
                        secondary={
                          <Typography 
                            variant="caption" 
                            sx={{ fontFamily: 'Cairo, sans-serif' }}
                          >
                            البريد الإلكتروني
                          </Typography>
                        }
                      />
                    </ListItem>
                  )}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Hall Information */}
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Box className="flex items-center space-x-2 space-x-reverse mb-3">
                  <HallIcon color="primary" />
                  <Typography 
                    variant="h6" 
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    معلومات القاعة
                  </Typography>
                </Box>
                
                <Typography 
                  variant="h6" 
                  className="font-semibold mb-2"
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  {booking.hall_name || 'غير محدد'}
                </Typography>
                
                <Typography 
                  variant="body2" 
                  className="text-gray-600 mb-2"
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  {booking.hall_type || 'قاعة عامة'}
                </Typography>
                
                <Typography 
                  variant="body2" 
                  className="text-gray-600"
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  السعة: {booking.hall_capacity || 'غير محدد'} شخص
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Booking Details */}
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardContent>
                <Box className="flex items-center space-x-2 space-x-reverse mb-3">
                  <ScheduleIcon color="primary" />
                  <Typography 
                    variant="h6" 
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    تفاصيل الحجز
                  </Typography>
                </Box>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} md={3}>
                    <Typography 
                      variant="body2" 
                      className="text-gray-600"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      التاريخ
                    </Typography>
                    <Typography 
                      variant="body1" 
                      className="font-medium"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {formatDate(booking.date)}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={3}>
                    <Typography 
                      variant="body2" 
                      className="text-gray-600"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      وقت البداية
                    </Typography>
                    <Typography 
                      variant="body1" 
                      className="font-medium"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {formatTime(booking.start_time)}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={3}>
                    <Typography 
                      variant="body2" 
                      className="text-gray-600"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      وقت النهاية
                    </Typography>
                    <Typography 
                      variant="body1" 
                      className="font-medium"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {formatTime(booking.end_time)}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={3}>
                    <Typography 
                      variant="body2" 
                      className="text-gray-600"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      المدة
                    </Typography>
                    <Typography 
                      variant="body1" 
                      className="font-medium"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {booking.duration_hours} ساعة
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Payment Information */}
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardContent>
                <Box className="flex items-center space-x-2 space-x-reverse mb-3">
                  <PaymentIcon color="primary" />
                  <Typography 
                    variant="h6" 
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    معلومات الدفع
                  </Typography>
                </Box>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} md={3}>
                    <Typography 
                      variant="body2" 
                      className="text-gray-600"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      السعر لكل ساعة
                    </Typography>
                    <Typography 
                      variant="body1" 
                      className="font-medium"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {booking.price_per_hour} ر.س
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={3}>
                    <Typography 
                      variant="body2" 
                      className="text-gray-600"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      المبلغ الإجمالي
                    </Typography>
                    <Typography 
                      variant="h6" 
                      className="font-bold text-green-600"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {booking.total_amount} ر.س
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={3}>
                    <Typography 
                      variant="body2" 
                      className="text-gray-600"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      طريقة الدفع
                    </Typography>
                    <Typography 
                      variant="body1" 
                      className="font-medium"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {getPaymentMethodText(booking.payment_method)}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={3}>
                    <Typography 
                      variant="body2" 
                      className="text-gray-600"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      حالة الدفع
                    </Typography>
                    <Chip
                      label={getPaymentStatusText(booking.payment_status)}
                      color={getPaymentStatusColor(booking.payment_status)}
                      size="small"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Notes */}
          {booking.notes && (
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Box className="flex items-center space-x-2 space-x-reverse mb-3">
                    <NotesIcon color="primary" />
                    <Typography 
                      variant="h6" 
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      ملاحظات
                    </Typography>
                  </Box>
                  
                  <Typography 
                    variant="body1" 
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    {booking.notes}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          )}

          {/* System Information */}
          <Grid item xs={12}>
            <Card variant="outlined" className="bg-gray-50">
              <CardContent>
                <Typography 
                  variant="subtitle2" 
                  className="font-semibold mb-2"
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  معلومات النظام
                </Typography>
                
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography 
                      variant="caption" 
                      className="text-gray-600"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      تاريخ الإنشاء: {formatDateTime(booking.created_at)}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <Typography 
                      variant="caption" 
                      className="text-gray-600"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      آخر تحديث: {formatDateTime(booking.updated_at)}
                    </Typography>
                  </Grid>
                  
                  {booking.created_by && (
                    <Grid item xs={12}>
                      <Typography 
                        variant="caption" 
                        className="text-gray-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        تم الإنشاء بواسطة: {booking.created_by_name || `المستخدم #${booking.created_by}`}
                      </Typography>
                    </Grid>
                  )}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions className="p-4">
        <Button 
          onClick={onClose}
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          إغلاق
        </Button>
        {canEdit && (
          <Button
            variant="contained"
            onClick={onEdit}
            startIcon={<EditIcon />}
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            تعديل
          </Button>
        )}
      </DialogActions>
    </>
  )
}

export default BookingDetails
