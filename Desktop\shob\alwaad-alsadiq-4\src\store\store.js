import { configureStore } from '@reduxjs/toolkit'
import authReducer from './slices/authSlice'
import hallsReducer from './slices/hallsSlice'
import bookingsReducer from './slices/bookingsSlice'
import usersReducer from './slices/usersSlice'
import reportsReducer from './slices/reportsSlice'
import notificationsReducer from './slices/notificationsSlice'

export const store = configureStore({
  reducer: {
    auth: authReducer,
    halls: hallsReducer,
    bookings: bookingsReducer,
    users: usersReducer,
    reports: reportsReducer,
    notifications: notificationsReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
})
