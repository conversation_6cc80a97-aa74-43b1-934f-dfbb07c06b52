@echo off
echo ========================================
echo    نظام إدارة قاعات الوعد الصادق 4
echo ========================================
echo.

echo جاري تثبيت المكتبات...
call npm install

echo.
echo جاري تشغيل الخادم...
start "Server" cmd /k "npm run server"

timeout /t 3 /nobreak > nul

echo.
echo جاري تشغيل واجهة المستخدم...
start "Frontend" cmd /k "npm run dev"

echo.
echo تم تشغيل النظام بنجاح!
echo.
echo واجهة المستخدم: http://localhost:3000
echo الخادم: http://localhost:5000
echo.
echo بيانات الدخول الافتراضية:
echo البريد الإلكتروني: <EMAIL>
echo كلمة المرور: admin123
echo.
pause
