import React, { useState, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  MenuItem,
  Typography,
  Box,
  Alert,
  IconButton,
  Stepper,
  Step,
  StepLabel,
  Card,
  CardContent,
  Chip
} from '@mui/material'
import {
  Close as CloseIcon,
  Person as PersonIcon,
  MeetingRoom as HallIcon,
  Schedule as ScheduleIcon,
  Payment as PaymentIcon
} from '@mui/icons-material'
import { DatePicker, TimePicker } from '@mui/x-date-pickers'
import dayjs from 'dayjs'

import { createBooking, updateBooking } from '../../store/slices/bookingsSlice'
import { fetchHalls } from '../../store/slices/hallsSlice'

const BookingForm = ({ booking, onClose, onSuccess }) => {
  const dispatch = useDispatch()
  const { halls } = useSelector((state) => state.halls)
  const { user } = useSelector((state) => state.auth)
  
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [activeStep, setActiveStep] = useState(0)
  
  const [formData, setFormData] = useState({
    hallId: '',
    customerName: '',
    customerPhone: '',
    customerEmail: '',
    date: null,
    startTime: null,
    endTime: null,
    notes: '',
    paymentMethod: 'cash'
  })

  const [calculatedData, setCalculatedData] = useState({
    duration: 0,
    pricePerHour: 0,
    totalAmount: 0
  })

  const steps = ['معلومات العميل', 'تفاصيل الحجز', 'المراجعة والدفع']

  useEffect(() => {
    dispatch(fetchHalls())
  }, [dispatch])

  useEffect(() => {
    if (booking) {
      setFormData({
        hallId: booking.hall_id || '',
        customerName: booking.customer_name || '',
        customerPhone: booking.customer_phone || '',
        customerEmail: booking.customer_email || '',
        date: booking.date ? dayjs(booking.date) : null,
        startTime: booking.start_time ? dayjs(`2000-01-01T${booking.start_time}`) : null,
        endTime: booking.end_time ? dayjs(`2000-01-01T${booking.end_time}`) : null,
        notes: booking.notes || '',
        paymentMethod: booking.payment_method || 'cash'
      })
    }
  }, [booking])

  useEffect(() => {
    calculateTotal()
  }, [formData.hallId, formData.startTime, formData.endTime])

  const calculateTotal = () => {
    if (formData.hallId && formData.startTime && formData.endTime) {
      const selectedHall = halls.find(h => h.id === parseInt(formData.hallId))
      if (selectedHall) {
        const start = formData.startTime
        const end = formData.endTime
        const duration = end.diff(start, 'hour', true)
        
        if (duration > 0) {
          const totalAmount = duration * selectedHall.price_per_hour
          setCalculatedData({
            duration: duration,
            pricePerHour: selectedHall.price_per_hour,
            totalAmount: totalAmount
          })
        }
      }
    }
  }

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const validateStep = (step) => {
    switch (step) {
      case 0:
        return formData.customerName && formData.customerPhone
      case 1:
        return formData.hallId && formData.date && formData.startTime && formData.endTime
      case 2:
        return calculatedData.totalAmount > 0
      default:
        return false
    }
  }

  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep(prev => prev + 1)
      setError('')
    } else {
      setError('يرجى ملء جميع الحقول المطلوبة')
    }
  }

  const handleBack = () => {
    setActiveStep(prev => prev - 1)
    setError('')
  }

  const handleSubmit = async () => {
    setLoading(true)
    setError('')

    try {
      const bookingData = {
        hall_id: parseInt(formData.hallId),
        customer_name: formData.customerName,
        customer_phone: formData.customerPhone,
        customer_email: formData.customerEmail,
        date: formData.date.format('YYYY-MM-DD'),
        start_time: formData.startTime.format('HH:mm'),
        end_time: formData.endTime.format('HH:mm'),
        duration_hours: calculatedData.duration,
        price_per_hour: calculatedData.pricePerHour,
        total_amount: calculatedData.totalAmount,
        notes: formData.notes,
        payment_method: formData.paymentMethod,
        status: 'pending'
      }

      if (booking) {
        await dispatch(updateBooking({ id: booking.id, bookingData })).unwrap()
      } else {
        await dispatch(createBooking(bookingData)).unwrap()
      }
      
      onSuccess()
    } catch (error) {
      setError(error.message || 'حدث خطأ أثناء حفظ الحجز')
    } finally {
      setLoading(false)
    }
  }

  const getSelectedHall = () => {
    return halls.find(h => h.id === parseInt(formData.hallId))
  }

  return (
    <>
      <DialogTitle className="flex justify-between items-center">
        <Typography 
          variant="h6" 
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          {booking ? 'تعديل الحجز' : 'حجز جديد'}
        </Typography>
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers className="space-y-4">
        {error && (
          <Alert severity="error" sx={{ fontFamily: 'Cairo, sans-serif' }}>
            {error}
          </Alert>
        )}

        {/* Stepper */}
        <Stepper activeStep={activeStep} className="mb-6">
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel sx={{ '& .MuiStepLabel-label': { fontFamily: 'Cairo, sans-serif' } }}>
                {label}
              </StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* Step 1: Customer Information */}
        {activeStep === 0 && (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Box className="flex items-center space-x-2 space-x-reverse mb-4">
                <PersonIcon color="primary" />
                <Typography 
                  variant="h6" 
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  معلومات العميل
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="اسم العميل"
                value={formData.customerName}
                onChange={(e) => handleChange('customerName', e.target.value)}
                sx={{
                  '& .MuiInputLabel-root': {
                    fontFamily: 'Cairo, sans-serif'
                  }
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="رقم الهاتف"
                value={formData.customerPhone}
                onChange={(e) => handleChange('customerPhone', e.target.value)}
                sx={{
                  '& .MuiInputLabel-root': {
                    fontFamily: 'Cairo, sans-serif'
                  }
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                type="email"
                label="البريد الإلكتروني (اختياري)"
                value={formData.customerEmail}
                onChange={(e) => handleChange('customerEmail', e.target.value)}
                sx={{
                  '& .MuiInputLabel-root': {
                    fontFamily: 'Cairo, sans-serif'
                  }
                }}
              />
            </Grid>
          </Grid>
        )}

        {/* Step 2: Booking Details */}
        {activeStep === 1 && (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Box className="flex items-center space-x-2 space-x-reverse mb-4">
                <HallIcon color="primary" />
                <Typography 
                  variant="h6" 
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  تفاصيل الحجز
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                select
                required
                label="القاعة"
                value={formData.hallId}
                onChange={(e) => handleChange('hallId', e.target.value)}
                sx={{
                  '& .MuiInputLabel-root': {
                    fontFamily: 'Cairo, sans-serif'
                  }
                }}
              >
                {halls.map((hall) => (
                  <MenuItem key={hall.id} value={hall.id}>
                    <div className="flex justify-between items-center w-full">
                      <span>{hall.name}</span>
                      <Chip 
                        label={`${hall.price_per_hour} ر.س/ساعة`}
                        size="small"
                        color="primary"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      />
                    </div>
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            <Grid item xs={12} md={4}>
              <DatePicker
                label="التاريخ"
                value={formData.date}
                onChange={(value) => handleChange('date', value)}
                minDate={dayjs()}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: true,
                    sx: {
                      '& .MuiInputLabel-root': {
                        fontFamily: 'Cairo, sans-serif'
                      }
                    }
                  }
                }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TimePicker
                label="وقت البداية"
                value={formData.startTime}
                onChange={(value) => handleChange('startTime', value)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: true,
                    sx: {
                      '& .MuiInputLabel-root': {
                        fontFamily: 'Cairo, sans-serif'
                      }
                    }
                  }
                }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TimePicker
                label="وقت النهاية"
                value={formData.endTime}
                onChange={(value) => handleChange('endTime', value)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: true,
                    sx: {
                      '& .MuiInputLabel-root': {
                        fontFamily: 'Cairo, sans-serif'
                      }
                    }
                  }
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="ملاحظات إضافية"
                value={formData.notes}
                onChange={(e) => handleChange('notes', e.target.value)}
                sx={{
                  '& .MuiInputLabel-root': {
                    fontFamily: 'Cairo, sans-serif'
                  }
                }}
              />
            </Grid>
          </Grid>
        )}

        {/* Step 3: Review and Payment */}
        {activeStep === 2 && (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Box className="flex items-center space-x-2 space-x-reverse mb-4">
                <PaymentIcon color="primary" />
                <Typography 
                  variant="h6" 
                  sx={{ fontFamily: 'Cairo, sans-serif' }}
                >
                  المراجعة والدفع
                </Typography>
              </Box>
            </Grid>

            {/* Booking Summary */}
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography 
                    variant="h6" 
                    className="mb-4"
                    sx={{ fontFamily: 'Cairo, sans-serif' }}
                  >
                    ملخص الحجز
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography 
                        variant="body2" 
                        className="text-gray-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        العميل:
                      </Typography>
                      <Typography 
                        variant="body1" 
                        className="font-medium"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        {formData.customerName}
                      </Typography>
                    </Grid>
                    
                    <Grid item xs={6}>
                      <Typography 
                        variant="body2" 
                        className="text-gray-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        القاعة:
                      </Typography>
                      <Typography 
                        variant="body1" 
                        className="font-medium"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        {getSelectedHall()?.name}
                      </Typography>
                    </Grid>
                    
                    <Grid item xs={6}>
                      <Typography 
                        variant="body2" 
                        className="text-gray-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        التاريخ:
                      </Typography>
                      <Typography 
                        variant="body1" 
                        className="font-medium"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        {formData.date?.format('YYYY-MM-DD')}
                      </Typography>
                    </Grid>
                    
                    <Grid item xs={6}>
                      <Typography 
                        variant="body2" 
                        className="text-gray-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        الوقت:
                      </Typography>
                      <Typography 
                        variant="body1" 
                        className="font-medium"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        {formData.startTime?.format('HH:mm')} - {formData.endTime?.format('HH:mm')}
                      </Typography>
                    </Grid>
                    
                    <Grid item xs={6}>
                      <Typography 
                        variant="body2" 
                        className="text-gray-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        المدة:
                      </Typography>
                      <Typography 
                        variant="body1" 
                        className="font-medium"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        {calculatedData.duration} ساعة
                      </Typography>
                    </Grid>
                    
                    <Grid item xs={6}>
                      <Typography 
                        variant="body2" 
                        className="text-gray-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        المبلغ الإجمالي:
                      </Typography>
                      <Typography 
                        variant="h6" 
                        className="font-bold text-green-600"
                        sx={{ fontFamily: 'Cairo, sans-serif' }}
                      >
                        {calculatedData.totalAmount} ر.س
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            {/* Payment Method */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                select
                label="طريقة الدفع"
                value={formData.paymentMethod}
                onChange={(e) => handleChange('paymentMethod', e.target.value)}
                sx={{
                  '& .MuiInputLabel-root': {
                    fontFamily: 'Cairo, sans-serif'
                  }
                }}
              >
                <MenuItem value="cash">نقداً</MenuItem>
                <MenuItem value="card">بطاقة ائتمان</MenuItem>
                <MenuItem value="bank_transfer">تحويل بنكي</MenuItem>
                <MenuItem value="online">دفع إلكتروني</MenuItem>
              </TextField>
            </Grid>
          </Grid>
        )}
      </DialogContent>

      <DialogActions className="p-4">
        <Button 
          onClick={onClose}
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          إلغاء
        </Button>
        
        {activeStep > 0 && (
          <Button
            onClick={handleBack}
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            السابق
          </Button>
        )}
        
        {activeStep < steps.length - 1 ? (
          <Button
            variant="contained"
            onClick={handleNext}
            disabled={!validateStep(activeStep)}
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            التالي
          </Button>
        ) : (
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={loading || !validateStep(activeStep)}
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            {loading ? 'جاري الحفظ...' : (booking ? 'تحديث الحجز' : 'تأكيد الحجز')}
          </Button>
        )}
      </DialogActions>
    </>
  )
}

export default BookingForm
