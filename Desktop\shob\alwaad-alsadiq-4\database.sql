-- نظام إدارة قاعات الوعد الصادق 4
-- ملف إنشاء قاعدة البيانات

-- إنشاء قاعدة البيانات
CREATE DATABASE alwaad_alsadiq_4;

-- الاتصال بقاعدة البيانات
\c alwaad_alsadiq_4;

-- جدول المستخدمين
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'customer' CHECK (role IN ('admin', 'manager', 'receptionist', 'customer')),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول القاعات
CREATE TABLE halls (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) DEFAULT 'general' CHECK (type IN ('vip', 'meeting', 'conference', 'wedding', 'gaming', 'general')),
    capacity INTEGER NOT NULL CHECK (capacity > 0),
    price_per_hour DECIMAL(10,2) NOT NULL CHECK (price_per_hour > 0),
    location VARCHAR(255),
    amenities TEXT[],
    rules TEXT[],
    working_hours JSONB DEFAULT '{"start": "08:00", "end": "23:00"}',
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance')),
    images TEXT[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الحجوزات
CREATE TABLE bookings (
    id SERIAL PRIMARY KEY,
    hall_id INTEGER REFERENCES halls(id) ON DELETE CASCADE,
    customer_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    customer_name VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(20),
    customer_email VARCHAR(255),
    date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    duration_hours DECIMAL(4,2) NOT NULL CHECK (duration_hours > 0),
    price_per_hour DECIMAL(10,2) NOT NULL CHECK (price_per_hour > 0),
    total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount > 0),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'completed', 'cancelled')),
    notes TEXT,
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'partial', 'refunded')),
    payment_method VARCHAR(50),
    created_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- التأكد من عدم تداخل الحجوزات
    CONSTRAINT no_overlapping_bookings EXCLUDE USING gist (
        hall_id WITH =,
        daterange(date, date, '[]') WITH &&,
        timerange(start_time, end_time, '[)') WITH &&
    ) WHERE (status != 'cancelled')
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error', 'booking', 'payment')),
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المدفوعات
CREATE TABLE payments (
    id SERIAL PRIMARY KEY,
    booking_id INTEGER REFERENCES bookings(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    payment_method VARCHAR(50) NOT NULL,
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'completed', 'failed', 'refunded')),
    transaction_id VARCHAR(255),
    payment_date TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول إعدادات النظام
CREATE TABLE system_settings (
    id SERIAL PRIMARY KEY,
    setting_key VARCHAR(255) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهارس لتحسين الأداء
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);

CREATE INDEX idx_halls_type ON halls(type);
CREATE INDEX idx_halls_status ON halls(status);

CREATE INDEX idx_bookings_hall_id ON bookings(hall_id);
CREATE INDEX idx_bookings_customer_id ON bookings(customer_id);
CREATE INDEX idx_bookings_date ON bookings(date);
CREATE INDEX idx_bookings_status ON bookings(status);
CREATE INDEX idx_bookings_created_at ON bookings(created_at);

CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_read ON notifications(read);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);

CREATE INDEX idx_payments_booking_id ON payments(booking_id);
CREATE INDEX idx_payments_status ON payments(payment_status);

-- دوال مساعدة
-- دالة لحساب مدة الحجز
CREATE OR REPLACE FUNCTION calculate_duration(start_time TIME, end_time TIME)
RETURNS DECIMAL(4,2) AS $$
BEGIN
    RETURN EXTRACT(EPOCH FROM (end_time - start_time)) / 3600.0;
END;
$$ LANGUAGE plpgsql;

-- دالة لحساب المبلغ الإجمالي
CREATE OR REPLACE FUNCTION calculate_total_amount(duration DECIMAL(4,2), price_per_hour DECIMAL(10,2))
RETURNS DECIMAL(10,2) AS $$
BEGIN
    RETURN duration * price_per_hour;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إضافة triggers لتحديث updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_halls_updated_at BEFORE UPDATE ON halls
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON bookings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_settings_updated_at BEFORE UPDATE ON system_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إدراج بيانات أولية
-- إنشاء مستخدم مدير افتراضي
INSERT INTO users (name, email, password, role) VALUES 
('مدير النظام', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

-- إدراج قاعات تجريبية
INSERT INTO halls (name, description, type, capacity, price_per_hour, location, amenities, rules) VALUES 
(
    'قاعة VIP الذهبية',
    'قاعة فاخرة مجهزة بأحدث التقنيات والمرافق الحديثة',
    'vip',
    100,
    500.00,
    'الطابق الثاني',
    ARRAY['تكييف مركزي', 'نظام صوتي متطور', 'إضاءة LED', 'واي فاي مجاني', 'شاشة عرض كبيرة'],
    ARRAY['ممنوع التدخين', 'الالتزام بالمواعيد', 'عدم إحضار المأكولات من الخارج']
),
(
    'قاعة الاجتماعات الرئيسية',
    'قاعة مثالية للاجتماعات والمؤتمرات الصغيرة والمتوسطة',
    'meeting',
    50,
    200.00,
    'الطابق الأول',
    ARRAY['بروجكتر', 'شاشة عرض كبيرة', 'نظام صوتي', 'طاولة اجتماعات', 'واي فاي'],
    ARRAY['الحضور قبل 15 دقيقة', 'إغلاق الهواتف المحمولة']
),
(
    'قاعة الألعاب الترفيهية',
    'قاعة مخصصة للألعاب والأنشطة الترفيهية للشباب',
    'gaming',
    30,
    150.00,
    'الطابق الأرضي',
    ARRAY['ألعاب إلكترونية', 'طاولات بلياردو', 'نظام ترفيهي', 'مقاعد مريحة'],
    ARRAY['للأعمار فوق 12 سنة', 'المحافظة على الأجهزة', 'عدم الإزعاج']
),
(
    'قاعة المؤتمرات الكبرى',
    'قاعة واسعة للمؤتمرات والفعاليات الكبيرة',
    'conference',
    200,
    800.00,
    'الطابق الثالث',
    ARRAY['نظام صوتي احترافي', 'إضاءة مسرحية', 'شاشات متعددة', 'مسرح صغير'],
    ARRAY['حجز مسبق بـ 48 ساعة', 'تنسيق مع الإدارة', 'الالتزام بالبروتوكول']
);

-- إدراج إعدادات النظام الافتراضية
INSERT INTO system_settings (setting_key, setting_value, description) VALUES 
('company_name', 'الوعد الصادق 4', 'اسم الشركة'),
('company_phone', '+966501234567', 'رقم هاتف الشركة'),
('company_email', '<EMAIL>', 'البريد الإلكتروني للشركة'),
('working_hours_start', '08:00', 'بداية ساعات العمل'),
('working_hours_end', '23:00', 'نهاية ساعات العمل'),
('booking_advance_days', '30', 'عدد الأيام المسموح بالحجز مقدماً'),
('cancellation_hours', '24', 'عدد الساعات المطلوبة للإلغاء'),
('currency', 'SAR', 'العملة المستخدمة'),
('tax_rate', '15', 'معدل الضريبة المضافة');

-- عرض ملخص قاعدة البيانات
SELECT 'تم إنشاء قاعدة البيانات بنجاح!' as message;
SELECT 'عدد الجداول المنشأة: ' || count(*) as tables_count 
FROM information_schema.tables 
WHERE table_schema = 'public';

-- عرض بيانات الدخول الافتراضية
SELECT 'بيانات الدخول الافتراضية:' as info;
SELECT 'البريد الإلكتروني: <EMAIL>' as email;
SELECT 'كلمة المرور: admin123' as password;
