import React from 'react'
import {
  <PERSON>,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  Divider,
  Avatar,
  Chip
} from '@mui/material'
import {
  EventNote as BookingIcon,
  Payment as PaymentIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  CheckCircle as SuccessIcon,
  MarkEmailRead as MarkReadIcon
} from '@mui/icons-material'
import { formatDistanceToNow } from 'date-fns'
import { ar } from 'date-fns/locale'

const NotificationsList = ({ 
  notifications, 
  onNotificationClick, 
  onMarkAllAsRead, 
  onClose 
}) => {
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'booking':
        return <BookingIcon color="primary" />
      case 'payment':
        return <PaymentIcon color="success" />
      case 'warning':
        return <WarningIcon color="warning" />
      case 'success':
        return <SuccessIcon color="success" />
      default:
        return <InfoIcon color="info" />
    }
  }

  const getNotificationColor = (type) => {
    switch (type) {
      case 'booking':
        return '#0ea5e9'
      case 'payment':
        return '#10b981'
      case 'warning':
        return '#f59e0b'
      case 'success':
        return '#10b981'
      default:
        return '#6b7280'
    }
  }

  const formatNotificationTime = (date) => {
    try {
      return formatDistanceToNow(new Date(date), { 
        addSuffix: true, 
        locale: ar 
      })
    } catch (error) {
      return 'منذ قليل'
    }
  }

  if (!notifications || notifications.length === 0) {
    return (
      <Box className="p-4 text-center">
        <Typography 
          variant="body2" 
          className="text-gray-500"
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          لا توجد إشعارات جديدة
        </Typography>
      </Box>
    )
  }

  return (
    <Box>
      {/* Header */}
      <Box className="p-3 border-b border-gray-200 flex justify-between items-center">
        <Typography 
          variant="h6" 
          className="font-semibold text-gray-800"
          sx={{ fontFamily: 'Cairo, sans-serif' }}
        >
          الإشعارات
        </Typography>
        {notifications.some(n => !n.read) && (
          <Button
            size="small"
            onClick={onMarkAllAsRead}
            startIcon={<MarkReadIcon />}
            sx={{ 
              fontFamily: 'Cairo, sans-serif',
              fontSize: '12px'
            }}
          >
            قراءة الكل
          </Button>
        )}
      </Box>

      {/* Notifications List */}
      <List className="max-h-80 overflow-y-auto p-0">
        {notifications.map((notification, index) => (
          <React.Fragment key={notification.id}>
            <ListItem
              button
              onClick={() => onNotificationClick(notification)}
              className={`py-3 px-4 hover:bg-gray-50 transition-colors ${
                !notification.read ? 'bg-blue-50' : ''
              }`}
            >
              <ListItemIcon className="min-w-0 mr-3">
                <Avatar
                  sx={{
                    bgcolor: getNotificationColor(notification.type),
                    width: 32,
                    height: 32
                  }}
                >
                  {getNotificationIcon(notification.type)}
                </Avatar>
              </ListItemIcon>
              
              <ListItemText
                primary={
                  <Box className="flex justify-between items-start">
                    <Typography
                      variant="body2"
                      className={`font-medium ${
                        !notification.read ? 'text-gray-900' : 'text-gray-700'
                      }`}
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {notification.title}
                    </Typography>
                    {!notification.read && (
                      <Chip
                        size="small"
                        label="جديد"
                        color="primary"
                        sx={{
                          height: 16,
                          fontSize: '10px',
                          fontFamily: 'Cairo, sans-serif'
                        }}
                      />
                    )}
                  </Box>
                }
                secondary={
                  <Box className="mt-1">
                    <Typography
                      variant="caption"
                      className="text-gray-600 block"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {notification.message}
                    </Typography>
                    <Typography
                      variant="caption"
                      className="text-gray-400 mt-1 block"
                      sx={{ fontFamily: 'Cairo, sans-serif' }}
                    >
                      {formatNotificationTime(notification.createdAt)}
                    </Typography>
                  </Box>
                }
              />
            </ListItem>
            {index < notifications.length - 1 && <Divider />}
          </React.Fragment>
        ))}
      </List>

      {/* Footer */}
      <Box className="p-3 border-t border-gray-200 text-center">
        <Button
          size="small"
          onClick={onClose}
          sx={{ 
            fontFamily: 'Cairo, sans-serif',
            fontSize: '12px'
          }}
        >
          إغلاق
        </Button>
      </Box>
    </Box>
  )
}

export default NotificationsList
