import React from 'react'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts'
import { Box, Typography } from '@mui/material'

const RevenueChart = () => {
  // Sample data - in real app, this would come from props or Redux
  const data = [
    { month: 'يناير', revenue: 45000, bookings: 120 },
    { month: 'فبراير', revenue: 52000, bookings: 140 },
    { month: 'مارس', revenue: 48000, bookings: 130 },
    { month: 'أبريل', revenue: 61000, bookings: 165 },
    { month: 'مايو', revenue: 55000, bookings: 150 },
    { month: 'يونيو', revenue: 67000, bookings: 180 },
    { month: 'يوليو', revenue: 71000, bookings: 195 },
    { month: 'أغسطس', revenue: 69000, bookings: 185 },
    { month: 'سبتمبر', revenue: 58000, bookings: 160 },
    { month: 'أكتوبر', revenue: 63000, bookings: 170 },
    { month: 'نوفمبر', revenue: 72000, bookings: 200 },
    { month: 'ديسمبر', revenue: 78000, bookings: 210 }
  ]

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <Typography
            variant="body2"
            className="font-medium mb-2"
            sx={{ fontFamily: 'Cairo, sans-serif' }}
          >
            {label}
          </Typography>
          {payload.map((entry, index) => (
            <div key={index} className="flex items-center space-x-2 space-x-reverse">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: entry.color }}
              />
              <Typography
                variant="caption"
                sx={{ fontFamily: 'Cairo, sans-serif' }}
              >
                {entry.name === 'revenue' ? 'الإيرادات' : 'الحجوزات'}: {entry.value.toLocaleString()}
                {entry.name === 'revenue' ? ' ر.س' : ''}
              </Typography>
            </div>
          ))}
        </div>
      )
    }
    return null
  }

  return (
    <Box className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <defs>
            <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#0ea5e9" stopOpacity={0.3}/>
              <stop offset="95%" stopColor="#0ea5e9" stopOpacity={0}/>
            </linearGradient>
            <linearGradient id="bookingsGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#10b981" stopOpacity={0.3}/>
              <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
            </linearGradient>
          </defs>
          
          <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
          
          <XAxis 
            dataKey="month" 
            axisLine={false}
            tickLine={false}
            tick={{ 
              fontSize: 12, 
              fontFamily: 'Cairo, sans-serif',
              fill: '#64748b'
            }}
          />
          
          <YAxis 
            axisLine={false}
            tickLine={false}
            tick={{ 
              fontSize: 12, 
              fontFamily: 'Cairo, sans-serif',
              fill: '#64748b'
            }}
            tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
          />
          
          <Tooltip content={<CustomTooltip />} />
          
          <Area
            type="monotone"
            dataKey="revenue"
            stroke="#0ea5e9"
            strokeWidth={3}
            fill="url(#revenueGradient)"
            name="revenue"
          />
          
          <Line
            type="monotone"
            dataKey="bookings"
            stroke="#10b981"
            strokeWidth={2}
            dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: '#10b981', strokeWidth: 2 }}
            name="bookings"
          />
        </AreaChart>
      </ResponsiveContainer>
    </Box>
  )
}

export default RevenueChart
